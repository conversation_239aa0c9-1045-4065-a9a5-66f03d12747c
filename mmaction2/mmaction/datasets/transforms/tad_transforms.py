# Copyright (c) OpenMMLab. All rights reserved.
import numpy as np
import torch
import torch.nn.functional as F
from mmcv.transforms import BaseTransform

from mmaction.registry import TRANSFORMS


@TRANSFORMS.register_module()
class ResizeLocalizationFeature(BaseTransform):
    """Resize localization features to a fixed temporal dimension.
    
    This transform is specifically designed for TAD (Temporal Action Detection) tasks
    where features need to be resized to a consistent temporal length.
    
    Required Keys:
        - raw_feature
        
    Modified Keys:
        - raw_feature
        
    Args:
        temporal_dim (int): Target temporal dimension to resize to.
        interpolation_mode (str): Interpolation mode for resizing. 
            Options: 'linear', 'nearest'. Default: 'linear'.
    """
    
    def __init__(self, temporal_dim, interpolation_mode='linear'):
        self.temporal_dim = temporal_dim
        self.interpolation_mode = interpolation_mode
        
        if interpolation_mode not in ['linear', 'nearest']:
            raise ValueError(f"interpolation_mode must be 'linear' or 'nearest', "
                           f"got {interpolation_mode}")
    
    def transform(self, results):
        """Perform the ResizeLocalizationFeature transform.
        
        Args:
            results (dict): The resulting dict to be modified and passed
                to the next transform in pipeline.
        """
        raw_feature = results['raw_feature']  # Shape: (feat_dim, original_temporal_dim)
        
        # Convert to tensor for interpolation
        feature_tensor = torch.from_numpy(raw_feature).float()
        
        # Add batch dimension: (1, feat_dim, original_temporal_dim)
        feature_tensor = feature_tensor.unsqueeze(0)
        
        # Resize using interpolation
        # F.interpolate expects (N, C, L) format for 1D interpolation
        resized_feature = F.interpolate(
            feature_tensor,
            size=self.temporal_dim,
            mode=self.interpolation_mode,
            align_corners=False if self.interpolation_mode == 'linear' else None
        )
        
        # Remove batch dimension and convert back to numpy
        resized_feature = resized_feature.squeeze(0).numpy()
        
        # Update the results
        results['raw_feature'] = resized_feature
        
        return results
    
    def __repr__(self):
        repr_str = (f'{self.__class__.__name__}('
                   f'temporal_dim={self.temporal_dim}, '
                   f'interpolation_mode={self.interpolation_mode})')
        return repr_str


@TRANSFORMS.register_module()
class PadLocalizationFeature(BaseTransform):
    """Pad or truncate localization features to a fixed temporal dimension.
    
    This transform is an alternative to ResizeLocalizationFeature that uses
    padding/truncation instead of interpolation.
    
    Required Keys:
        - raw_feature
        
    Modified Keys:
        - raw_feature
        
    Args:
        temporal_dim (int): Target temporal dimension.
        pad_mode (str): Padding mode. Options: 'constant', 'reflect', 'replicate'.
            Default: 'constant'.
        pad_value (float): Value to use for constant padding. Default: 0.0.
        truncate_mode (str): How to truncate if sequence is longer than temporal_dim.
            Options: 'center', 'start', 'end'. Default: 'center'.
    """
    
    def __init__(self, temporal_dim, pad_mode='constant', pad_value=0.0, truncate_mode='center'):
        self.temporal_dim = temporal_dim
        self.pad_mode = pad_mode
        self.pad_value = pad_value
        self.truncate_mode = truncate_mode
        
        if pad_mode not in ['constant', 'reflect', 'replicate']:
            raise ValueError(f"pad_mode must be one of ['constant', 'reflect', 'replicate'], "
                           f"got {pad_mode}")
        
        if truncate_mode not in ['center', 'start', 'end']:
            raise ValueError(f"truncate_mode must be one of ['center', 'start', 'end'], "
                           f"got {truncate_mode}")
    
    def transform(self, results):
        """Perform the PadLocalizationFeature transform.
        
        Args:
            results (dict): The resulting dict to be modified and passed
                to the next transform in pipeline.
        """
        raw_feature = results['raw_feature']  # Shape: (feat_dim, original_temporal_dim)
        feat_dim, original_temporal_dim = raw_feature.shape
        
        if original_temporal_dim == self.temporal_dim:
            # No change needed
            return results
        elif original_temporal_dim < self.temporal_dim:
            # Need to pad
            pad_length = self.temporal_dim - original_temporal_dim
            
            if self.pad_mode == 'constant':
                pad_array = np.full((feat_dim, pad_length), self.pad_value, dtype=raw_feature.dtype)
                padded_feature = np.concatenate([raw_feature, pad_array], axis=1)
            elif self.pad_mode == 'reflect':
                if original_temporal_dim == 1:
                    # Special case: if only 1 frame, replicate it
                    pad_array = np.tile(raw_feature, (1, pad_length))
                else:
                    # Reflect padding
                    pad_array = np.flip(raw_feature[:, -min(pad_length, original_temporal_dim):], axis=1)
                    if pad_length > original_temporal_dim:
                        # Need more padding, repeat the process
                        remaining = pad_length - original_temporal_dim
                        additional_pad = np.tile(raw_feature, (1, (remaining // original_temporal_dim) + 1))[:, :remaining]
                        pad_array = np.concatenate([pad_array, additional_pad], axis=1)
                padded_feature = np.concatenate([raw_feature, pad_array], axis=1)
            elif self.pad_mode == 'replicate':
                # Replicate the last frame
                last_frame = raw_feature[:, -1:]
                pad_array = np.tile(last_frame, (1, pad_length))
                padded_feature = np.concatenate([raw_feature, pad_array], axis=1)
            
            results['raw_feature'] = padded_feature
        else:
            # Need to truncate
            if self.truncate_mode == 'center':
                start_idx = (original_temporal_dim - self.temporal_dim) // 2
                end_idx = start_idx + self.temporal_dim
            elif self.truncate_mode == 'start':
                start_idx = 0
                end_idx = self.temporal_dim
            else:  # 'end'
                start_idx = original_temporal_dim - self.temporal_dim
                end_idx = original_temporal_dim
            
            truncated_feature = raw_feature[:, start_idx:end_idx]
            results['raw_feature'] = truncated_feature
        
        return results
    
    def __repr__(self):
        repr_str = (f'{self.__class__.__name__}('
                   f'temporal_dim={self.temporal_dim}, '
                   f'pad_mode={self.pad_mode}, '
                   f'pad_value={self.pad_value}, '
                   f'truncate_mode={self.truncate_mode})')
        return repr_str
