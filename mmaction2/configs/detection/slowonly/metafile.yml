Collections:
  - Name: SlowOnly
    README: configs/detection/slowonly/README.md
    Paper:
      URL: https://arxiv.org/abs/1812.03982
      Title: 'SlowFast Networks for Video Recognition'

Models:
  - Name: slowonly_kinetics400-pretrained-r50_8xb16-4x16x1-20e_ava21-rgb
    Config: configs/detection/slowonly/slowonly_kinetics400-pretrained-r50_8xb16-4x16x1-20e_ava21-rgb.py
    In Collection: SlowOnly
    Metadata:
      Architecture: ResNet50
      Batch Size: 16
      Epochs: 20
      Pretrained: Kinetics-400
      Training Data: AVA v2.1
      Training Resources: 8 GPUs
    Modality: RGB
    Results:
      - Dataset: AVA v2.1
        Task: Action Detection
        Metrics:
              mAP: 20.72
    Training Log: https://download.openmmlab.com/mmaction/v1.0/detection/slowonly/slowonly_kinetics400-pretrained-r50_8xb16-4x16x1-20e_ava21-rgb/slowonly_kinetics400-pretrained-r50_8xb16-4x16x1-20e_ava21-rgb.log
    Weights: https://download.openmmlab.com/mmaction/v1.0/detection/slowonly/slowonly_kinetics400-pretrained-r50_8xb16-4x16x1-20e_ava21-rgb/slowonly_kinetics400-pretrained-r50_8xb16-4x16x1-20e_ava21-rgb_20220906-953ef5fe.pth

  - Name: slowonly_kinetics700-pretrained-r50_8xb16-4x16x1-20e_ava21-rgb
    Config: configs/detection/slowonly/slowonly_kinetics700-pretrained-r50_8xb16-4x16x1-20e_ava21-rgb.py
    In Collection: SlowOnly
    Metadata:
      Architecture: ResNet50
      Batch Size: 16
      Epochs: 20
      Pretrained: Kinetics-700
      Training Data: AVA v2.1
      Training Resources: 8 GPUs
    Modality: RGB
    Results:
      - Dataset: AVA v2.1
        Task: Action Detection
        Metrics:
              mAP: 22.77
    Training Log: https://download.openmmlab.com/mmaction/v1.0/detection/slowonly/slowonly_kinetics700-pretrained-r50_8xb16-4x16x1-20e_ava21-rgb/slowonly_kinetics700-pretrained-r50_8xb16-4x16x1-20e_ava21-rgb.log
    Weights: https://download.openmmlab.com/mmaction/v1.0/detection/slowonly/slowonly_kinetics700-pretrained-r50_8xb16-4x16x1-20e_ava21-rgb/slowonly_kinetics700-pretrained-r50_8xb16-4x16x1-20e_ava21-rgb_20220906-b3b6d44e.pth

  - Name: slowonly_kinetics400-pretrained-r50-nl_8xb16-4x16x1-20e_ava21-rgb
    Config: configs/detection/slowonly/slowonly_kinetics400-pretrained-r50-nl_8xb16-4x16x1-20e_ava21-rgb.py
    In Collection: SlowOnly
    Metadata:
      Architecture: ResNet50
      Batch Size: 16
      Epochs: 20
      Pretrained: Kinetics-400
      Training Data: AVA v2.1
      Training Resources: 8 GPUs
    Modality: RGB
    Results:
      - Dataset: AVA v2.1
        Task: Action Detection
        Metrics:
              mAP: 21.55
    Training Log: https://download.openmmlab.com/mmaction/v1.0/detection/slowonly/slowonly_kinetics400-pretrained-r50-nl_8xb16-4x16x1-20e_ava21-rgb/slowonly_kinetics400-pretrained-r50-nl_8xb16-4x16x1-20e_ava21-rgb.log
    Weights: https://download.openmmlab.com/mmaction/v1.0/detection/slowonly/slowonly_kinetics400-pretrained-r50-nl_8xb16-4x16x1-20e_ava21-rgb/slowonly_kinetics400-pretrained-r50-nl_8xb16-4x16x1-20e_ava21-rgb_20220906-5ae3f91b.pth

  - Name: slowonly_kinetics400-pretrained-r50-nl_8xb16-8x8x1-20e_ava21-rgb
    Config: configs/detection/slowonly/slowonly_kinetics400-pretrained-r50-nl_8xb16-8x8x1-20e_ava21-rgb.py
    In Collection: SlowOnly
    Metadata:
      Architecture: ResNet50
      Batch Size: 16
      Epochs: 20
      Pretrained: Kinetics-400
      Training Data: AVA v2.1
      Training Resources: 8 GPUs
    Modality: RGB
    Results:
      - Dataset: AVA v2.1
        Task: Action Detection
        Metrics:
              mAP: 23.77
    Training Log: https://download.openmmlab.com/mmaction/v1.0/detection/slowonly/slowonly_kinetics400-pretrained-r50-nl_8xb16-8x8x1-20e_ava21-rgb/slowonly_kinetics400-pretrained-r50-nl_8xb16-8x8x1-20e_ava21-rgb.log
    Weights: https://download.openmmlab.com/mmaction/v1.0/detection/slowonly/slowonly_kinetics400-pretrained-r50-nl_8xb16-8x8x1-20e_ava21-rgb/slowonly_kinetics400-pretrained-r50-nl_8xb16-8x8x1-20e_ava21-rgb_20220906-9760eadb.pth

  - Name: slowonly_kinetics400-pretrained-r101_8xb16-8x8x1-20e_ava21-rgb
    Config: configs/detection/slowonly/slowonly_kinetics400-pretrained-r101_8xb16-8x8x1-20e_ava21-rgb.py
    In Collection: SlowOnly
    Metadata:
      Architecture: ResNet101
      Batch Size: 16
      Epochs: 20
      Pretrained: Kinetics-400
      Training Data: AVA v2.1
      Training Resources: 8 GPUs
    Modality: RGB
    Results:
      - Dataset: AVA v2.1
        Task: Action Detection
        Metrics:
              mAP: 24.83
    Training Log: https://download.openmmlab.com/mmaction/v1.0/detection/slowonly/slowonly_kinetics400-pretrained-r101_8xb16-8x8x1-20e_ava21-rgb/slowonly_kinetics400-pretrained-r101_8xb16-8x8x1-20e_ava21-rgb.log
    Weights: https://download.openmmlab.com/mmaction/v1.0/detection/slowonly/slowonly_kinetics400-pretrained-r101_8xb16-8x8x1-20e_ava21-rgb/slowonly_kinetics400-pretrained-r101_8xb16-8x8x1-20e_ava21-rgb_20220906-43f16877.pth

  - Name: slowonly_kinetics400-pretrained-r50_8xb16-4x16x1-8e_multisports-rgb
    Config: configs/detection/slowonly/slowonly_kinetics400-pretrained-r50_8xb16-4x16x1-8e_multisports-rgb.py
    In Collection: SlowOnly
    Metadata:
      Architecture: ResNet50
      Batch Size: 16
      Epochs: 8
      Pretrained: Kinetics-400
      Resolution: short-side 320
      Training Data: MultiSports
      Training Resources: 8 GPUs
    Modality: RGB
    Results:
      - Dataset: MultiSports
        Task: Action Detection
        Metrics:
              f-mAP: 26.40
    Training Log: https://download.openmmlab.com/mmaction/v1.0/detection/slowonly/slowonly_kinetics400-pretrained-r50_8xb16-4x16x1-8e_multisports-rgb/slowonly_kinetics400-pretrained-r50_8xb16-4x16x1-8e_multisports-rgb.log
    Weights: https://download.openmmlab.com/mmaction/v1.0/detection/slowonly/slowonly_kinetics400-pretrained-r50_8xb16-4x16x1-8e_multisports-rgb/slowonly_kinetics400-pretrained-r50_8xb16-4x16x1-8e_multisports-rgb_20230320-a1ca5e76.pth
