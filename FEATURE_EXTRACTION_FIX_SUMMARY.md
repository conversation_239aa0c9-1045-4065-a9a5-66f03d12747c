# BMN推理脚本特征提取修复总结

## 问题描述

原始的BMN推理脚本在第287行使用了随机生成的特征向量作为占位符，而不是真正的SlowOnly特征提取：

```python
# 原始问题代码
feature_vector = np.random.randn(2048)  # 占位符特征
```

这导致推理阶段与数据预处理阶段的特征提取方式不一致，影响模型性能。

## 修复方案

### 1. 使用MMAction2官方特征提取工具

修改 `extract_slowonly_features` 函数，使用与数据预处理阶段相同的MMAction2官方特征提取工具：

```python
# 使用MMAction2的clip_feature_extraction.py
extraction_script = os.path.abspath(os.path.join(mmaction2_root, "tools/misc/clip_feature_extraction.py"))
```

### 2. 统一特征提取参数

确保推理阶段使用与数据预处理阶段相同的参数：

```python
cmd = [
    'python', extraction_script,
    config_path,
    checkpoint_path,
    temp_feature_dir,
    '--video-list', temp_video_list,
    '--video-root', video_dir,
    '--long-video-mode',
    '--clip-interval', '32',  # 与数据预处理保持一致
    '--frame-interval', '2',   # 与数据预处理保持一致
    '--spatial-type', 'avg',   # 空间维度平均池化
    '--temporal-type', 'avg'   # 时间维度平均池化
]
```

### 3. 解决路径问题

将所有相对路径转换为绝对路径，避免"No such file or directory"错误：

```python
# 获取绝对路径
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(script_dir, "../../.."))

# 配置文件路径（绝对路径）
config_path = os.path.abspath(os.path.join(project_root, 
                            "data_process/preprocessing/feature_extraction/slowonly_r50_feature_extraction_config.py"))

# 预训练模型路径（绝对路径）
checkpoint_path = os.path.abspath(os.path.join(project_root, 
                                "work_dirs/checkpoints/slowonly_r50_kinetics400.pth"))
```

### 4. 处理特征文件格式

正确处理MMAction2生成的pkl特征文件：

```python
# 处理PyTorch tensor
if hasattr(features, 'cpu'):
    features = features.cpu().numpy()
elif hasattr(features, 'numpy'):
    features = features.numpy()

# 处理文件名（MMAction2可能生成完整文件名）
if not os.path.exists(pkl_file):
    pkl_file_full = os.path.abspath(os.path.join(temp_feature_dir, f"{video_name}.pkl"))
    if os.path.exists(pkl_file_full):
        pkl_file = pkl_file_full
```

### 5. 添加特征池化函数

实现与数据预处理后处理脚本一致的特征池化函数：

```python
def pool_feature_to_100_steps(data, num_proposals=100, pool_type='mean'):
    """将任意长度的特征池化到100个时间步"""
    # 计算每个proposal对应的时间范围
    duration = len(data)
    proposal_duration = duration / num_proposals
    
    pooled_features = []
    for i in range(num_proposals):
        start_idx = int(i * proposal_duration)
        end_idx = int((i + 1) * proposal_duration)
        # ... 池化逻辑
```

## 修复结果

### 测试验证

运行测试脚本验证修复效果：

```bash
cd /home/<USER>/johnny_ws/mmaction2_ws
conda activate mmaction2-tad
python test/test_simple_inference.py
```

### 测试结果

✅ **特征提取测试通过**
- 原始特征形状: torch.Size([8, 2048])
- 转换后特征形状: (8, 2048)
- 池化前特征形状: (8, 2048)
- 池化后特征形状: (100, 2048)
- 最终特征形状: (2048, 100)

✅ **关键改进**
1. 移除了随机特征生成（第287行）
2. 使用真正的SlowOnly特征提取
3. 与数据预处理阶段保持一致的参数
4. 解决了路径问题
5. 正确处理PyTorch tensor和numpy数组转换

## 使用方法

修复后的推理脚本使用方法：

```bash
python data_process/inference/scripts/bmn_inference.py \
    --video /path/to/video.mp4 \
    --config data_process/training/configs/bmn_multiclass_tad_config.py \
    --work-dir work_dirs/bmn_multiclass_tad_slowonly \
    --output-dir ./inference_results \
    --extract-features \
    --confidence-threshold 0.5
```

## 注意事项

1. **预训练模型**: 确保SlowOnly预训练模型已下载到 `work_dirs/checkpoints/slowonly_r50_kinetics400.pth`

2. **环境配置**: 确保MMAction2环境正确配置，conda环境为 `mmaction2-tad`

3. **特征一致性**: 现在推理阶段的特征提取与数据预处理阶段完全一致，确保了模型性能

4. **路径处理**: 所有路径都使用绝对路径，避免了工作目录相关的问题

## 总结

通过这次修复，BMN推理脚本现在：
- ✅ 使用真正的SlowOnly特征提取
- ✅ 与数据预处理阶段保持一致
- ✅ 解决了路径问题
- ✅ 正确处理特征格式转换
- ✅ 提供了完整的特征提取流水线

这确保了推理阶段能够获得与训练阶段一致的高质量特征，从而提高模型的推理准确性。
