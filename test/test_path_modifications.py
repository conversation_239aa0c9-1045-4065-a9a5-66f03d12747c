#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试路径修改是否正确
"""

import sys
import os
sys.path.append('../data_process/preprocessing/data_organization')

from extract_video_info import extract_video_info

def test_path_generation():
    """测试路径生成逻辑"""
    
    print("🧪 测试路径生成修改")
    print("=" * 60)
    
    # 测试用例
    test_cases = [
        "20250727T075940Z_20250727T080440Z-00.01.01.023-00.01.03.193.mp4",
        "video_sample-00.00.05.000-00.00.10.000.mp4",
        "test_video.mp4-00.02.00.000-00.02.05.000.mp4"
    ]
    
    for filename in test_cases:
        print(f"\n📝 测试文件名: {filename}")
        
        # 提取视频信息
        video_info = extract_video_info(filename)
        
        if video_info:
            original_video_name, start_time, end_time = video_info
            
            # 模拟生成路径的逻辑
            original_video_path = f"/home/<USER>/johnny_ws/mmaction2_ws/data/raw_videos/{original_video_name}.mp4"
            feature_path = f"/home/<USER>/johnny_ws/mmaction2_ws/data/MultiClassTAD/features_slowonly_reconstructed/{original_video_name.replace('.mp4', '')}.csv"
            
            print(f"   原始视频名: {original_video_name}")
            print(f"   时间段: {start_time:.3f}s - {end_time:.3f}s")
            print(f"   原始视频路径: {original_video_path}")
            print(f"   特征文件路径: {feature_path}")
            
            # 验证修改
            print(f"   ✅ 原始视频路径不包含 '_decrypted_roi': {'_decrypted_roi' not in original_video_path}")
            print(f"   ✅ 特征文件名不包含 '.mp4': {'.mp4' not in os.path.basename(feature_path)}")
            
        else:
            print(f"   ❌ 无法解析文件名: {filename}")

def main():
    """主函数"""
    print("🎯 测试路径修改")
    
    try:
        test_path_generation()
        print("\n✅ 路径修改测试完成!")
        print("\n📝 修改总结:")
        print("1. ✅ 原始视频路径已去除 '_decrypted_roi' 后缀")
        print("2. ✅ 特征文件路径中的文件名不再包含 '.mp4'")
        print("\n🚀 现在可以使用修改后的脚本处理您的视频文件了!")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
