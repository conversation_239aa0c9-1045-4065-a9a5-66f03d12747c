#!/usr/bin/env python3
"""
TAD数据集重构运行脚本
一键运行数据集重构和验证流程
"""

import sys
import os
from pathlib import Path
import argparse

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from reconstruct_tad_dataset import TADDatasetReconstructor
from validate_reconstructed_dataset import DatasetValidator

def main():
    parser = argparse.ArgumentParser(description='TAD数据集重构工具')
    parser.add_argument('--data-root', 
                       default='/home/<USER>/johnny_ws/mmaction2_ws/data/MultiClassTAD',
                       help='数据集根目录路径')
    parser.add_argument('--raw-videos-root',
                       default='/home/<USER>/johnny_ws/mmaction2_ws/data/raw_videos',
                       help='原始视频文件根目录路径')
    parser.add_argument('--train-ratio',
                       type=float,
                       default=0.8,
                       help='训练集比例 (默认: 0.8)')
    parser.add_argument('--skip-validation',
                       action='store_true',
                       help='跳过验证步骤')
    parser.add_argument('--only-validate',
                       action='store_true',
                       help='仅运行验证，不重构数据集')
    
    args = parser.parse_args()
    
    print("TAD数据集重构工具")
    print("=" * 50)
    print(f"数据集根目录: {args.data_root}")
    print(f"原始视频目录: {args.raw_videos_root}")
    print(f"训练集比例: {args.train_ratio}")
    print("=" * 50)
    
    # 检查目录是否存在
    data_root = Path(args.data_root)
    raw_videos_root = Path(args.raw_videos_root)
    
    if not data_root.exists():
        print(f"❌ 数据集目录不存在: {data_root}")
        return False
    
    if not args.only_validate and not raw_videos_root.exists():
        print(f"❌ 原始视频目录不存在: {raw_videos_root}")
        print("提示: 如果只想验证已有的重构数据集，请使用 --only-validate 参数")
        return False
    
    success = True
    
    # 数据集重构
    if not args.only_validate:
        print("\n🔄 开始数据集重构...")
        try:
            reconstructor = TADDatasetReconstructor(
                data_root=str(data_root),
                raw_videos_root=str(raw_videos_root)
            )
            
            # 修改create_reconstructed_annotations方法调用
            segments_by_video = reconstructor.collect_segments_info()
            train_data, val_data = reconstructor.create_reconstructed_annotations(
                segments_by_video, train_ratio=args.train_ratio
            )
            
            train_file, val_file = reconstructor.run()
            print(f"✅ 数据集重构完成!")
            print(f"   训练集文件: {train_file}")
            print(f"   验证集文件: {val_file}")
            
        except Exception as e:
            print(f"❌ 数据集重构失败: {e}")
            import traceback
            traceback.print_exc()
            success = False
    
    # 数据集验证
    if success and not args.skip_validation:
        print("\n🔍 开始数据集验证...")
        try:
            validator = DatasetValidator(data_root=str(data_root))
            validation_passed = validator.run_validation()
            
            if validation_passed:
                print("✅ 数据集验证通过!")
            else:
                print("⚠️ 数据集验证发现问题，请检查日志")
                success = False
                
        except Exception as e:
            print(f"❌ 数据集验证失败: {e}")
            import traceback
            traceback.print_exc()
            success = False
    
    # 生成使用说明
    if success and not args.only_validate:
        print("\n📋 使用说明:")
        print("1. 重构后的数据集文件已生成:")
        print(f"   - 训练集: {data_root}/multiclass_tad_train_reconstructed.json")
        print(f"   - 验证集: {data_root}/multiclass_tad_val_reconstructed.json")
        print("\n2. 更新MMAction2配置文件:")
        print("   将配置文件中的ann_file_train和ann_file_val指向新的JSON文件")
        print("\n3. 重新提取特征:")
        print("   由于现在使用完整视频，需要重新提取特征文件")
        print("\n4. 调整模型参数:")
        print("   可能需要调整temporal_dim等参数以适应更长的视频")
        
        # 生成示例配置
        config_example = f"""
# 示例配置更新:
ann_file_train = '{data_root}/multiclass_tad_train_reconstructed.json'
ann_file_val = '{data_root}/multiclass_tad_val_reconstructed.json'

# 可能需要调整的模型参数:
model = dict(
    type='BMN',
    temporal_dim=300,  # 根据视频长度调整
    num_samples=100,
    # ... 其他参数
)
"""
        
        config_file = data_root / "config_update_example.py"
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(config_example)
        print(f"\n5. 配置示例已保存到: {config_file}")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
