#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的特征提取流水线
"""

import os
import sys
import subprocess
from pathlib import Path

def test_pipeline():
    """测试特征提取流水线"""
    
    # 获取脚本路径
    pipeline_script = Path('../data_process/utilities/pipeline/run_feature_extraction_pipeline.py')
    
    if not pipeline_script.exists():
        print(f"❌ 流水线脚本不存在: {pipeline_script}")
        return False
    
    print("🧪 测试特征提取流水线")
    print("=" * 60)
    
    # 测试帮助信息
    print("\n📋 测试帮助信息...")
    try:
        result = subprocess.run(['python', str(pipeline_script), '--help'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 帮助信息正常")
            print("输出:")
            print(result.stdout)
        else:
            print("❌ 帮助信息失败")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ 执行帮助命令时出错: {e}")
        return False
    
    # 测试前置条件检查（不实际运行流水线）
    print("\n🔍 测试前置条件检查...")
    print("注意: 这只是测试脚本能否正常启动，不会实际运行完整流水线")
    
    return True

def main():
    """主函数"""
    print("🎯 测试修改后的特征提取流水线")
    
    if test_pipeline():
        print("\n✅ 流水线脚本测试通过!")
        print("\n📝 使用说明:")
        print("1. 确保分割视频文件在 /home/<USER>/johnny_ws/mmaction2_ws/data/segmented_videos/ 目录下")
        print("2. 运行完整流水线:")
        print("   cd /home/<USER>/johnny_ws/mmaction2_ws/data_process/utilities/pipeline")
        print("   python run_feature_extraction_pipeline.py --non-interactive")
        print("3. 或者交互式运行:")
        print("   python run_feature_extraction_pipeline.py")
        print("4. 跳过某些步骤:")
        print("   python run_feature_extraction_pipeline.py --skip-video-info")
    else:
        print("\n❌ 流水线脚本测试失败!")

if __name__ == '__main__':
    main()
