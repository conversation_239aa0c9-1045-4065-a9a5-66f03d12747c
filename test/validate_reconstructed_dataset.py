#!/usr/bin/env python3
"""
重构数据集验证脚本
验证重构后的TAD数据集的质量和完整性
"""

import json
import os
from pathlib import Path
import matplotlib.pyplot as plt
import numpy as np
from collections import defaultdict, Counter

class DatasetValidator:
    def __init__(self, data_root="/home/<USER>/johnny_ws/mmaction2_ws/data/MultiClassTAD"):
        self.data_root = Path(data_root)
        
    def load_annotations(self):
        """加载重构后的标注文件"""
        train_file = self.data_root / "multiclass_tad_train_reconstructed.json"
        val_file = self.data_root / "multiclass_tad_val_reconstructed.json"
        
        train_data = {}
        val_data = {}
        
        if train_file.exists():
            with open(train_file, 'r', encoding='utf-8') as f:
                train_data = json.load(f)
        else:
            print(f"训练集文件不存在: {train_file}")
            
        if val_file.exists():
            with open(val_file, 'r', encoding='utf-8') as f:
                val_data = json.load(f)
        else:
            print(f"验证集文件不存在: {val_file}")
            
        return train_data, val_data
    
    def validate_format(self, data, dataset_name):
        """验证数据格式是否符合ActivityNetDataset标准"""
        print(f"\n验证{dataset_name}数据格式...")
        
        required_fields = ['duration_second', 'duration_frame', 'annotations', 'feature_frame', 'fps', 'rfps']
        annotation_fields = ['segment', 'label']
        
        format_errors = []
        
        for video_id, video_info in data.items():
            # 检查必需字段
            for field in required_fields:
                if field not in video_info:
                    format_errors.append(f"视频 {video_id} 缺少字段: {field}")
            
            # 检查annotations格式
            if 'annotations' in video_info:
                for i, ann in enumerate(video_info['annotations']):
                    for field in annotation_fields:
                        if field not in ann:
                            format_errors.append(f"视频 {video_id} 标注 {i} 缺少字段: {field}")
                    
                    # 检查segment格式
                    if 'segment' in ann:
                        if not isinstance(ann['segment'], list) or len(ann['segment']) != 2:
                            format_errors.append(f"视频 {video_id} 标注 {i} segment格式错误")
                        elif ann['segment'][0] >= ann['segment'][1]:
                            format_errors.append(f"视频 {video_id} 标注 {i} 时间段无效: {ann['segment']}")
        
        if format_errors:
            print(f"  发现 {len(format_errors)} 个格式错误:")
            for error in format_errors[:10]:  # 只显示前10个错误
                print(f"    - {error}")
            if len(format_errors) > 10:
                print(f"    ... 还有 {len(format_errors) - 10} 个错误")
        else:
            print(f"  ✅ {dataset_name}格式验证通过")
        
        return len(format_errors) == 0
    
    def analyze_temporal_distribution(self, data, dataset_name):
        """分析时间分布"""
        print(f"\n分析{dataset_name}时间分布...")
        
        video_durations = []
        segment_durations = []
        segments_per_video = []
        
        for video_info in data.values():
            video_durations.append(video_info['duration_second'])
            segments_per_video.append(len(video_info['annotations']))
            
            for ann in video_info['annotations']:
                segment_duration = ann['segment'][1] - ann['segment'][0]
                segment_durations.append(segment_duration)
        
        # 统计信息
        print(f"  视频时长统计:")
        print(f"    平均: {np.mean(video_durations):.2f}秒")
        print(f"    中位数: {np.median(video_durations):.2f}秒")
        print(f"    范围: {np.min(video_durations):.2f} - {np.max(video_durations):.2f}秒")
        
        print(f"  动作片段时长统计:")
        print(f"    平均: {np.mean(segment_durations):.2f}秒")
        print(f"    中位数: {np.median(segment_durations):.2f}秒")
        print(f"    范围: {np.min(segment_durations):.2f} - {np.max(segment_durations):.2f}秒")
        
        print(f"  每视频片段数统计:")
        print(f"    平均: {np.mean(segments_per_video):.2f}个")
        print(f"    中位数: {np.median(segments_per_video):.2f}个")
        print(f"    范围: {np.min(segments_per_video)} - {np.max(segments_per_video)}个")
        
        return {
            'video_durations': video_durations,
            'segment_durations': segment_durations,
            'segments_per_video': segments_per_video
        }
    
    def analyze_label_distribution(self, data, dataset_name):
        """分析标签分布"""
        print(f"\n分析{dataset_name}标签分布...")
        
        label_counts = Counter()
        label_durations = defaultdict(list)
        
        for video_info in data.values():
            for ann in video_info['annotations']:
                label = ann['label']
                duration = ann['segment'][1] - ann['segment'][0]
                
                label_counts[label] += 1
                label_durations[label].append(duration)
        
        print(f"  标签数量分布:")
        total_segments = sum(label_counts.values())
        for label, count in label_counts.most_common():
            percentage = count / total_segments * 100
            print(f"    {label}: {count} ({percentage:.1f}%)")
        
        print(f"  各标签平均时长:")
        for label, durations in label_durations.items():
            avg_duration = np.mean(durations)
            print(f"    {label}: {avg_duration:.2f}秒")
        
        return label_counts, label_durations
    
    def check_temporal_overlaps(self, data, dataset_name):
        """检查时间重叠问题"""
        print(f"\n检查{dataset_name}时间重叠...")
        
        overlap_count = 0
        total_videos = len(data)
        
        for video_id, video_info in data.items():
            annotations = video_info['annotations']
            
            # 按开始时间排序
            sorted_anns = sorted(annotations, key=lambda x: x['segment'][0])
            
            # 检查相邻片段是否重叠
            for i in range(len(sorted_anns) - 1):
                current_end = sorted_anns[i]['segment'][1]
                next_start = sorted_anns[i + 1]['segment'][0]
                
                if current_end > next_start:
                    overlap_count += 1
                    print(f"    重叠: 视频 {video_id}, 片段 {i} 和 {i+1}")
                    print(f"      {sorted_anns[i]['segment']} vs {sorted_anns[i+1]['segment']}")
        
        if overlap_count == 0:
            print(f"  ✅ 未发现时间重叠")
        else:
            print(f"  ⚠️ 发现 {overlap_count} 个时间重叠")
        
        return overlap_count
    
    def check_video_files(self, data, dataset_name):
        """检查原始视频文件是否存在"""
        print(f"\n检查{dataset_name}原始视频文件...")
        
        missing_files = []
        existing_files = 0
        
        for video_id, video_info in data.items():
            if 'original_video_path' in video_info:
                video_path = Path(video_info['original_video_path'])
                if video_path.exists():
                    existing_files += 1
                else:
                    missing_files.append(str(video_path))
        
        print(f"  存在的视频文件: {existing_files}")
        print(f"  缺失的视频文件: {len(missing_files)}")
        
        if missing_files:
            print(f"  缺失文件列表:")
            for file_path in missing_files[:5]:  # 只显示前5个
                print(f"    - {file_path}")
            if len(missing_files) > 5:
                print(f"    ... 还有 {len(missing_files) - 5} 个文件")
        
        return len(missing_files) == 0
    
    def generate_visualization(self, train_stats, val_stats):
        """生成可视化图表"""
        print("\n生成可视化图表...")
        
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        fig.suptitle('TAD数据集重构后统计分析', fontsize=16)
        
        # 视频时长分布
        axes[0, 0].hist(train_stats['video_durations'], bins=20, alpha=0.7, label='训练集')
        axes[0, 0].hist(val_stats['video_durations'], bins=20, alpha=0.7, label='验证集')
        axes[0, 0].set_title('视频时长分布')
        axes[0, 0].set_xlabel('时长(秒)')
        axes[0, 0].set_ylabel('频次')
        axes[0, 0].legend()
        
        # 动作片段时长分布
        axes[0, 1].hist(train_stats['segment_durations'], bins=30, alpha=0.7, label='训练集')
        axes[0, 1].hist(val_stats['segment_durations'], bins=30, alpha=0.7, label='验证集')
        axes[0, 1].set_title('动作片段时长分布')
        axes[0, 1].set_xlabel('时长(秒)')
        axes[0, 1].set_ylabel('频次')
        axes[0, 1].legend()
        
        # 每视频片段数分布
        axes[0, 2].hist(train_stats['segments_per_video'], bins=10, alpha=0.7, label='训练集')
        axes[0, 2].hist(val_stats['segments_per_video'], bins=10, alpha=0.7, label='验证集')
        axes[0, 2].set_title('每视频片段数分布')
        axes[0, 2].set_xlabel('片段数')
        axes[0, 2].set_ylabel('频次')
        axes[0, 2].legend()
        
        # 清空下方子图用于文本统计
        for i in range(3):
            axes[1, i].axis('off')
        
        plt.tight_layout()
        
        # 保存图表
        output_path = self.data_root / "dataset_analysis.png"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"  可视化图表已保存: {output_path}")
        
        return output_path
    
    def run_validation(self):
        """运行完整的验证流程"""
        print("开始验证重构后的TAD数据集...")
        
        # 加载数据
        train_data, val_data = self.load_annotations()
        
        if not train_data and not val_data:
            print("❌ 未找到重构后的数据集文件")
            return False
        
        validation_passed = True
        
        # 格式验证
        if train_data:
            validation_passed &= self.validate_format(train_data, "训练集")
        if val_data:
            validation_passed &= self.validate_format(val_data, "验证集")
        
        # 时间分布分析
        train_stats = self.analyze_temporal_distribution(train_data, "训练集") if train_data else None
        val_stats = self.analyze_temporal_distribution(val_data, "验证集") if val_data else None
        
        # 标签分布分析
        if train_data:
            self.analyze_label_distribution(train_data, "训练集")
        if val_data:
            self.analyze_label_distribution(val_data, "验证集")
        
        # 时间重叠检查
        if train_data:
            self.check_temporal_overlaps(train_data, "训练集")
        if val_data:
            self.check_temporal_overlaps(val_data, "验证集")
        
        # 视频文件检查
        if train_data:
            validation_passed &= self.check_video_files(train_data, "训练集")
        if val_data:
            validation_passed &= self.check_video_files(val_data, "验证集")
        
        # 生成可视化
        if train_stats and val_stats:
            self.generate_visualization(train_stats, val_stats)
        
        # 总结
        print(f"\n{'='*50}")
        if validation_passed:
            print("✅ 数据集验证通过！可以用于TAD模型训练")
        else:
            print("❌ 数据集验证发现问题，请检查并修复")
        print(f"{'='*50}")
        
        return validation_passed

if __name__ == "__main__":
    validator = DatasetValidator()
    validator.run_validation()
