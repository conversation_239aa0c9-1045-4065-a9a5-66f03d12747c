#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试numpy导入修复
验证prepare_inference_data函数中的numpy导入问题是否已解决
"""

import os
import sys
import numpy as np
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "data_process" / "inference" / "scripts"))

def create_test_feature_file():
    """创建测试特征文件"""
    test_dir = project_root / "test" / "numpy_test"
    test_dir.mkdir(exist_ok=True)
    
    # 创建测试特征文件 (2048, 100)
    features = np.random.randn(2048, 100)
    feature_file = test_dir / "test_features.csv"
    
    # 保存为CSV格式
    header = ','.join([f'time_{i}' for i in range(features.shape[1])])
    np.savetxt(feature_file, features, delimiter=',', header=header, comments='')
    
    print(f"✅ 创建测试特征文件: {feature_file}")
    print(f"   特征形状: {features.shape}")
    
    return str(feature_file)

def test_prepare_inference_data():
    """测试prepare_inference_data函数"""
    print("🧪 测试prepare_inference_data函数")
    print("=" * 60)
    
    try:
        # 导入修复后的函数
        from bmn_inference import prepare_inference_data
        
        # 创建测试特征文件
        feature_file = create_test_feature_file()
        
        # 创建测试视频信息
        video_info = {
            'duration_second': 10.0,
            'duration_frame': 250,
            'fps': 25.0,
            'frame_count': 250
        }
        
        print(f"\n🔧 测试数据准备...")
        print(f"   特征文件: {feature_file}")
        print(f"   视频信息: {video_info}")
        
        # 执行数据准备
        data = prepare_inference_data(feature_file, video_info)
        
        if data:
            print(f"✅ 数据准备成功")
            print(f"   特征文件: {data.get('feature_path', 'N/A')}")
            print(f"   视频名称: {data.get('video_name', 'N/A')}")
            print(f"   视频时长: {data.get('duration_second', 'N/A')}秒")
            print(f"   特征帧数: {data.get('feature_frame', 'N/A')}")
            return True
        else:
            print(f"❌ 数据准备失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_different_feature_shapes():
    """测试不同特征形状的处理"""
    print(f"\n🔧 测试不同特征形状的处理")
    
    try:
        from bmn_inference import prepare_inference_data
        
        test_dir = project_root / "test" / "numpy_test"
        
        # 测试用例：不同的特征形状
        test_cases = [
            ("标准格式 (2048, 100)", (2048, 100)),
            ("需要转置 (100, 2048)", (100, 2048)),
            ("需要重采样 (2048, 150)", (2048, 150)),
            ("需要转置和重采样 (80, 2048)", (80, 2048)),
        ]
        
        video_info = {
            'duration_second': 10.0,
            'duration_frame': 250,
            'fps': 25.0,
            'frame_count': 250
        }
        
        all_passed = True
        
        for case_name, shape in test_cases:
            print(f"\n   测试: {case_name}")
            
            # 创建测试特征
            features = np.random.randn(*shape)
            feature_file = test_dir / f"test_features_{shape[0]}x{shape[1]}.csv"
            
            # 保存特征文件
            if shape[1] == 100:
                header = ','.join([f'time_{i}' for i in range(shape[1])])
            else:
                header = ','.join([f'feature_{i}' for i in range(shape[1])])
            
            np.savetxt(feature_file, features, delimiter=',', header=header, comments='')
            
            # 测试数据准备
            try:
                data = prepare_inference_data(str(feature_file), video_info)
                if data:
                    print(f"     ✅ 成功处理 {shape}")
                else:
                    print(f"     ❌ 处理失败 {shape}")
                    all_passed = False
            except Exception as e:
                print(f"     ❌ 异常: {e}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 numpy导入修复测试")
    print("=" * 60)
    
    # 测试1: 基本功能
    test1_passed = test_prepare_inference_data()
    
    # 测试2: 不同特征形状
    test2_passed = test_different_feature_shapes()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print(f"   基本功能测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"   特征形状测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 numpy导入问题修复成功！")
        print("   - prepare_inference_data函数正常工作")
        print("   - 所有numpy操作都能正确执行")
        print("   - 支持不同的特征形状处理")
    else:
        print("\n⚠️  仍有问题需要解决")

if __name__ == '__main__':
    main()
