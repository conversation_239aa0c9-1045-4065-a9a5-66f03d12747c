#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特征提取一致性
验证推理阶段的特征提取与数据预处理阶段是否一致
"""

import os
import sys
import numpy as np
import subprocess
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_feature_extraction_consistency():
    """测试特征提取一致性"""
    print("🧪 测试特征提取一致性")
    print("=" * 60)
    
    # 测试视频路径（使用一个小的测试视频）
    test_video = project_root / "data" / "MultiClassTAD" / "videos" / "test_video.mp4"
    
    if not test_video.exists():
        print(f"❌ 测试视频不存在: {test_video}")
        print("   请准备一个测试视频文件")
        return False
    
    # 输出目录
    output_dir = project_root / "test" / "feature_extraction_test"
    output_dir.mkdir(exist_ok=True)
    
    print(f"📹 测试视频: {test_video}")
    print(f"📁 输出目录: {output_dir}")
    
    # 测试1: 使用数据预处理方法提取特征
    print("\n🔧 测试1: 数据预处理方法特征提取")
    preprocessing_features = test_preprocessing_extraction(test_video, output_dir)
    
    # 测试2: 使用推理方法提取特征
    print("\n🚀 测试2: 推理方法特征提取")
    inference_features = test_inference_extraction(test_video, output_dir)
    
    # 比较特征
    if preprocessing_features is not None and inference_features is not None:
        print("\n📊 比较特征一致性")
        compare_features(preprocessing_features, inference_features)
    else:
        print("\n❌ 特征提取失败，无法比较")
        return False
    
    return True

def test_preprocessing_extraction(video_path, output_dir):
    """测试数据预处理方法的特征提取"""
    try:
        # 创建临时视频列表
        video_list_file = output_dir / "temp_video_list.txt"
        with open(video_list_file, 'w') as f:
            f.write(f"{video_path.name}\n")
        
        # 配置文件路径
        config_path = project_root / "data_process" / "preprocessing" / "feature_extraction" / "slowonly_r50_feature_extraction_config.py"
        checkpoint_path = project_root / "work_dirs" / "checkpoints" / "slowonly_r50_kinetics400.pth"
        
        if not config_path.exists():
            print(f"   ❌ 配置文件不存在: {config_path}")
            return None
            
        if not checkpoint_path.exists():
            print(f"   ❌ 预训练模型不存在: {checkpoint_path}")
            return None
        
        # 特征输出目录
        feature_output_dir = output_dir / "preprocessing_features"
        feature_output_dir.mkdir(exist_ok=True)
        
        # 执行特征提取
        mmaction2_root = project_root / "mmaction2"
        extraction_script = mmaction2_root / "tools" / "misc" / "clip_feature_extraction.py"
        
        cmd = [
            'python', str(extraction_script),
            str(config_path),
            str(checkpoint_path),
            str(feature_output_dir),
            '--video-list', str(video_list_file),
            '--video-root', str(video_path.parent),
            '--long-video-mode',
            '--clip-interval', '32',
            '--frame-interval', '2',
            '--spatial-type', 'avg',
            '--temporal-type', 'avg'
        ]
        
        print(f"   执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=str(mmaction2_root))
        
        if result.returncode != 0:
            print(f"   ❌ 特征提取失败:")
            print(f"   stdout: {result.stdout}")
            print(f"   stderr: {result.stderr}")
            return None
        
        # 加载特征
        pkl_file = feature_output_dir / f"{video_path.stem}.pkl"
        if not pkl_file.exists():
            print(f"   ❌ 特征文件不存在: {pkl_file}")
            return None
        
        import pickle
        with open(pkl_file, 'rb') as f:
            features = pickle.load(f)
        
        print(f"   ✅ 数据预处理特征提取成功")
        print(f"   特征形状: {features.shape}")
        
        return features
        
    except Exception as e:
        print(f"   ❌ 数据预处理特征提取异常: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_inference_extraction(video_path, output_dir):
    """测试推理方法的特征提取"""
    try:
        # 导入推理脚本中的特征提取函数
        sys.path.insert(0, str(project_root / "data_process" / "inference" / "scripts"))
        from bmn_inference import extract_slowonly_features
        
        # 特征输出目录
        feature_output_dir = output_dir / "inference_features"
        feature_output_dir.mkdir(exist_ok=True)
        
        # 执行特征提取
        feature_file = extract_slowonly_features(str(video_path), str(feature_output_dir))
        
        if not feature_file:
            print(f"   ❌ 推理特征提取失败")
            return None
        
        # 加载CSV特征
        features = np.loadtxt(feature_file, delimiter=',', skiprows=1)
        
        print(f"   ✅ 推理特征提取成功")
        print(f"   特征形状: {features.shape}")
        
        return features
        
    except Exception as e:
        print(f"   ❌ 推理特征提取异常: {e}")
        import traceback
        traceback.print_exc()
        return None

def compare_features(preprocessing_features, inference_features):
    """比较两种方法提取的特征"""
    print(f"   数据预处理特征形状: {preprocessing_features.shape}")
    print(f"   推理特征形状: {inference_features.shape}")
    
    # 处理数据预处理特征到相同格式
    if len(preprocessing_features.shape) == 3:
        # (batch, feature_dim, time_steps) -> (feature_dim, time_steps)
        preprocessing_features = preprocessing_features.mean(axis=0)
    
    if len(preprocessing_features.shape) == 2:
        if preprocessing_features.shape[0] != 2048:
            # (time_steps, feature_dim) -> (feature_dim, time_steps)
            preprocessing_features = preprocessing_features.T
    
    print(f"   处理后数据预处理特征形状: {preprocessing_features.shape}")
    print(f"   推理特征形状: {inference_features.shape}")
    
    # 检查形状是否一致
    if preprocessing_features.shape != inference_features.shape:
        print(f"   ⚠️  特征形状不一致")
        return False
    
    # 计算相似度
    mse = np.mean((preprocessing_features - inference_features) ** 2)
    cosine_sim = np.dot(preprocessing_features.flatten(), inference_features.flatten()) / (
        np.linalg.norm(preprocessing_features.flatten()) * np.linalg.norm(inference_features.flatten())
    )
    
    print(f"   均方误差 (MSE): {mse:.6f}")
    print(f"   余弦相似度: {cosine_sim:.6f}")
    
    # 判断一致性
    if mse < 1e-6 and cosine_sim > 0.99:
        print(f"   ✅ 特征高度一致")
        return True
    elif mse < 1e-3 and cosine_sim > 0.95:
        print(f"   ✅ 特征基本一致")
        return True
    else:
        print(f"   ⚠️  特征存在差异")
        return False

def main():
    """主函数"""
    print("🎯 BMN特征提取一致性测试")
    print("=" * 60)
    
    success = test_feature_extraction_consistency()
    
    if success:
        print("\n✅ 测试完成")
    else:
        print("\n❌ 测试失败")

if __name__ == '__main__':
    main()
