#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的推理测试
测试修复后的特征提取功能
"""

import os
import sys
import subprocess
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def create_test_video():
    """创建一个简单的测试视频"""
    test_video_path = project_root / "test" / "test_video.mp4"
    
    if test_video_path.exists():
        print(f"✅ 测试视频已存在: {test_video_path}")
        return str(test_video_path)
    
    print(f"🎬 创建测试视频...")
    
    # 使用ffmpeg创建一个简单的测试视频（10秒，纯色）
    cmd = [
        'ffmpeg', '-y',
        '-f', 'lavfi',
        '-i', 'color=c=blue:size=224x224:duration=10',
        '-c:v', 'libx264',
        '-pix_fmt', 'yuv420p',
        str(test_video_path)
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print(f"✅ 测试视频创建成功: {test_video_path}")
            return str(test_video_path)
        else:
            print(f"❌ 测试视频创建失败: {result.stderr}")
            return None
    except subprocess.TimeoutExpired:
        print(f"❌ 测试视频创建超时")
        return None
    except Exception as e:
        print(f"❌ 测试视频创建异常: {e}")
        return None

def test_feature_extraction():
    """测试特征提取功能"""
    print("🧪 测试特征提取功能")
    print("=" * 60)
    
    # 创建测试视频
    test_video = create_test_video()
    if not test_video:
        print("❌ 无法创建测试视频，跳过测试")
        return False
    
    # 检查必要文件
    config_path = project_root / "data_process" / "preprocessing" / "feature_extraction" / "slowonly_r50_feature_extraction_config.py"
    checkpoint_path = project_root / "work_dirs" / "checkpoints" / "slowonly_r50_kinetics400.pth"
    
    if not config_path.exists():
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    
    if not checkpoint_path.exists():
        print(f"❌ 预训练模型不存在: {checkpoint_path}")
        print("   请先下载预训练模型")
        return False
    
    # 测试特征提取
    print(f"\n🔧 测试特征提取...")
    
    try:
        # 导入修复后的特征提取函数
        sys.path.insert(0, str(project_root / "data_process" / "inference" / "scripts"))
        from bmn_inference import extract_slowonly_features
        
        # 输出目录
        output_dir = project_root / "test" / "feature_test"
        output_dir.mkdir(exist_ok=True)
        
        # 执行特征提取
        print(f"   输入视频: {test_video}")
        print(f"   输出目录: {output_dir}")
        
        feature_file = extract_slowonly_features(test_video, str(output_dir))
        
        if feature_file and os.path.exists(feature_file):
            print(f"✅ 特征提取成功: {feature_file}")
            
            # 验证特征文件
            import numpy as np
            features = np.loadtxt(feature_file, delimiter=',', skiprows=1)
            print(f"   特征形状: {features.shape}")
            
            if features.shape == (2048, 100):
                print(f"✅ 特征形状正确")
                return True
            else:
                print(f"❌ 特征形状错误，期望(2048, 100)，得到{features.shape}")
                return False
        else:
            print(f"❌ 特征提取失败")
            return False
            
    except Exception as e:
        print(f"❌ 特征提取异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_bmn_inference_script():
    """测试BMN推理脚本"""
    print("\n🚀 测试BMN推理脚本")
    print("=" * 60)
    
    # 创建测试视频
    test_video = create_test_video()
    if not test_video:
        print("❌ 无法创建测试视频，跳过测试")
        return False
    
    # 检查BMN模型
    bmn_config = project_root / "data_process" / "training" / "configs" / "bmn_multiclass_tad_config.py"
    bmn_work_dir = project_root / "work_dirs" / "bmn_multiclass_tad_slowonly"
    
    if not bmn_config.exists():
        print(f"❌ BMN配置文件不存在: {bmn_config}")
        return False
    
    if not bmn_work_dir.exists():
        print(f"❌ BMN工作目录不存在: {bmn_work_dir}")
        print("   请先训练BMN模型")
        return False
    
    # 运行推理脚本
    print(f"\n🔧 运行推理脚本...")
    
    inference_script = project_root / "data_process" / "inference" / "scripts" / "bmn_inference.py"
    output_dir = project_root / "test" / "inference_test"
    
    cmd = [
        'python', str(inference_script),
        '--video', test_video,
        '--config', str(bmn_config),
        '--work-dir', str(bmn_work_dir),
        '--output-dir', str(output_dir),
        '--extract-features',
        '--confidence-threshold', '0.3'
    ]
    
    print(f"   命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300, cwd=str(project_root))
        
        if result.returncode == 0:
            print(f"✅ 推理脚本执行成功")
            print(f"   输出: {result.stdout[-500:]}")  # 显示最后500个字符
            return True
        else:
            print(f"❌ 推理脚本执行失败")
            print(f"   stderr: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ 推理脚本执行超时")
        return False
    except Exception as e:
        print(f"❌ 推理脚本执行异常: {e}")
        return False

def main():
    """主函数"""
    print("🎯 BMN推理脚本简单测试")
    print("=" * 60)
    
    # 测试1: 特征提取功能
    test1_passed = test_feature_extraction()
    
    # 测试2: 完整推理脚本（如果有BMN模型）
    test2_passed = test_bmn_inference_script()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print(f"   特征提取测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"   推理脚本测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed:
        print("\n🎉 特征提取修复成功！")
        print("   - 已移除随机特征生成")
        print("   - 使用真正的SlowOnly特征提取")
        print("   - 路径问题已解决")
        
        if not test2_passed:
            print("\n📝 注意:")
            print("   - 完整推理需要训练好的BMN模型")
            print("   - 请先完成BMN模型训练")
    else:
        print("\n⚠️  特征提取仍有问题，请检查:")
        print("   - 预训练模型是否已下载")
        print("   - MMAction2环境是否正确配置")

if __name__ == '__main__':
    main()
