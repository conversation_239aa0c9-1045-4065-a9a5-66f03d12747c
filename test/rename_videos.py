#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频文件重命名脚本
功能：
1. 检测文件名中 decrypted 后是否有 _roi
2. 如果没有，则在 decrypted 后插入 _roi
3. 删除文件名最后的 _roi
"""

import os
import re
import shutil
from pathlib import Path

def rename_videos(directory_path):
    """
    重命名指定目录下的mp4视频文件
    
    Args:
        directory_path (str): 视频文件所在目录路径
    """
    directory = Path(directory_path)
    
    if not directory.exists():
        print(f"错误：目录 {directory_path} 不存在")
        return
    
    # 获取所有mp4文件
    mp4_files = list(directory.glob("*.mp4"))
    
    if not mp4_files:
        print(f"在目录 {directory_path} 中没有找到mp4文件")
        return
    
    print(f"找到 {len(mp4_files)} 个mp4文件")
    
    renamed_count = 0
    
    for file_path in mp4_files:
        original_name = file_path.name
        print(f"\n处理文件: {original_name}")
        
        # 检查文件名是否包含 decrypted
        if "decrypted" not in original_name:
            print(f"  跳过：文件名不包含 'decrypted'")
            continue
        
        new_name = process_filename(original_name)
        
        if new_name == original_name:
            print(f"  无需重命名")
            continue
        
        # 构建新的文件路径
        new_file_path = file_path.parent / new_name
        
        # 检查新文件名是否已存在
        if new_file_path.exists():
            print(f"  警告：目标文件 {new_name} 已存在，跳过重命名")
            continue
        
        try:
            # 重命名文件
            file_path.rename(new_file_path)
            print(f"  重命名成功: {original_name} -> {new_name}")
            renamed_count += 1
        except Exception as e:
            print(f"  重命名失败: {e}")
    
    print(f"\n重命名完成！共处理 {renamed_count} 个文件")

def process_filename(filename):
    """
    处理单个文件名
    
    Args:
        filename (str): 原始文件名
        
    Returns:
        str: 处理后的文件名
    """
    # 分离文件名和扩展名
    name_part, ext = os.path.splitext(filename)
    
    # 检查文件名是否以 _roi 结尾
    if name_part.endswith('_roi'):
        # 删除最后的 _roi
        name_part = name_part[:-4]  # 删除最后4个字符 '_roi'
        print(f"  删除文件名末尾的 '_roi'")
    
    # 检查 decrypted 后面是否有 _roi
    # 使用正则表达式查找 decrypted 后面的内容
    pattern = r'(.*decrypted)([^_].*)'
    match = re.match(pattern, name_part)
    
    if match:
        # decrypted 后面没有 _roi，需要插入
        prefix = match.group(1)  # decrypted 之前的部分包括 decrypted
        suffix = match.group(2)  # decrypted 之后的部分
        name_part = f"{prefix}_roi{suffix}"
        print(f"  在 'decrypted' 后插入 '_roi'")
    else:
        # 检查是否已经有 _roi
        if 'decrypted_roi' in name_part:
            print(f"  'decrypted' 后已有 '_roi'，无需插入")
        else:
            print(f"  文件名格式不符合预期模式")
    
    return name_part + ext

def test_filename_processing():
    """测试文件名处理逻辑"""
    test_cases = [
        "20250727T075411Z_20250727T075911Z_decrypted-00.00.03.481-00.00.05.934-seg01_roi.mp4",
        "20250728T075409Z_20250728T075909Z_decrypted_roi-00.00.02.101-00.00.03.453-seg01.mp4"
    ]

    print("测试文件名处理逻辑:")
    print("=" * 50)

    for original in test_cases:
        processed = process_filename(original)
        print(f"原始: {original}")
        print(f"处理后: {processed}")
        print("-" * 50)

def main():
    """主函数"""
    # 目标目录
    video_directory = "/home/<USER>/johnny_ws/mmaction2_ws/data/MultiClassTAD/segmented_videos_ok"

    print("视频文件重命名脚本")
    print("=" * 50)
    print(f"目标目录: {video_directory}")

    # 先测试处理逻辑
    test_filename_processing()

    # 确认操作
    response = input("\n是否继续执行重命名操作？(y/N): ")
    if response.lower() not in ['y', 'yes']:
        print("操作已取消")
        return

    # 执行重命名
    rename_videos(video_directory)

if __name__ == "__main__":
    main()
