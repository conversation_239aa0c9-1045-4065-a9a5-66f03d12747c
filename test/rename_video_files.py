#!/usr/bin/env python3
"""
视频文件重命名脚本
用于遍历指定目录下的所有视频文件并重命名它们，移除不需要的后缀
"""

import os
import re
from pathlib import Path


def rename_video_files(base_dir):
    """
    遍历指定目录下的所有视频文件并重命名
    
    Args:
        base_dir (str): 基础目录路径
    """
    base_path = Path(base_dir)
    
    if not base_path.exists():
        print(f"错误: 目录 {base_dir} 不存在")
        return
    
    # 支持的视频文件扩展名
    video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'}
    
    # 需要移除的模式列表
    patterns_to_remove = [
        r'_decrypted',
        r'_roi',
        r' \(online-video-cutter\.com\)',
        r'-seg\d+',  # 移除 -seg1, -seg2 等
    ]
    
    renamed_count = 0
    
    # 递归遍历所有子目录
    for root, dirs, files in os.walk(base_path):
        for file in files:
            file_path = Path(root) / file
            
            # 检查是否为视频文件
            if file_path.suffix.lower() not in video_extensions:
                continue
            
            # 获取文件名（不包含扩展名）和扩展名
            file_stem = file_path.stem
            file_suffix = file_path.suffix
            
            # 应用所有移除模式
            new_file_stem = file_stem
            for pattern in patterns_to_remove:
                new_file_stem = re.sub(pattern, '', new_file_stem)
            
            # 如果文件名没有变化，跳过
            if new_file_stem == file_stem:
                print(f"跳过: {file_path} (无需重命名)")
                continue
            
            # 构建新的文件路径
            new_file_path = file_path.parent / (new_file_stem + file_suffix)
            
            # 检查新文件名是否已存在
            if new_file_path.exists():
                print(f"警告: 目标文件已存在，跳过重命名: {new_file_path}")
                continue
            
            try:
                # 重命名文件
                file_path.rename(new_file_path)
                print(f"重命名成功:")
                print(f"  原文件: {file_path}")
                print(f"  新文件: {new_file_path}")
                renamed_count += 1
            except Exception as e:
                print(f"重命名失败: {file_path}")
                print(f"错误信息: {e}")
    
    print(f"\n重命名完成! 总共重命名了 {renamed_count} 个文件")


def preview_rename(base_dir):
    """
    预览重命名操作，不实际执行重命名
    
    Args:
        base_dir (str): 基础目录路径
    """
    base_path = Path(base_dir)
    
    if not base_path.exists():
        print(f"错误: 目录 {base_dir} 不存在")
        return
    
    # 支持的视频文件扩展名
    video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'}
    
    # 需要移除的模式列表
    patterns_to_remove = [
        r'_decrypted',
        r'_roi',
        r' \(online-video-cutter\.com\)',
        r'-seg\d+',  # 移除 -seg1, -seg2 等
    ]
    
    preview_count = 0
    
    print("=== 重命名预览 ===")
    
    # 递归遍历所有子目录
    for root, dirs, files in os.walk(base_path):
        for file in files:
            file_path = Path(root) / file
            
            # 检查是否为视频文件
            if file_path.suffix.lower() not in video_extensions:
                continue
            
            # 获取文件名（不包含扩展名）和扩展名
            file_stem = file_path.stem
            file_suffix = file_path.suffix
            
            # 应用所有移除模式
            new_file_stem = file_stem
            for pattern in patterns_to_remove:
                new_file_stem = re.sub(pattern, '', new_file_stem)
            
            # 如果文件名没有变化，跳过
            if new_file_stem == file_stem:
                continue
            
            # 构建新的文件路径
            new_file_path = file_path.parent / (new_file_stem + file_suffix)
            
            print(f"将重命名:")
            print(f"  原文件: {file_path}")
            print(f"  新文件: {new_file_path}")
            print()
            preview_count += 1
    
    print(f"预览完成! 将重命名 {preview_count} 个文件")


if __name__ == "__main__":
    # 设置基础目录
    base_directory = "/home/<USER>/johnny_ws/mmaction2_ws/data/segmented_videos"
    
    print("视频文件重命名脚本")
    print("=" * 50)
    
    # 首先预览重命名操作
    preview_rename(base_directory)
    
    # 询问用户是否继续
    user_input = input("\n是否继续执行重命名操作? (y/N): ").strip().lower()
    
    if user_input in ['y', 'yes', '是']:
        print("\n开始执行重命名操作...")
        rename_video_files(base_directory)
    else:
        print("操作已取消")
