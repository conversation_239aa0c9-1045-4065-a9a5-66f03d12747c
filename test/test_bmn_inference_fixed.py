#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的BMN推理脚本
验证特征提取是否使用了正确的SlowOnly方法
"""

import os
import sys
import numpy as np
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "data_process" / "inference" / "scripts"))

def test_feature_extraction_method():
    """测试特征提取方法"""
    print("🧪 测试修复后的特征提取方法")
    print("=" * 60)
    
    try:
        # 导入修复后的推理脚本
        from bmn_inference import extract_slowonly_features, pool_feature_to_100_steps
        
        print("✅ 成功导入修复后的推理脚本")
        
        # 测试pool_feature_to_100_steps函数
        print("\n🔧 测试特征池化函数")
        
        # 创建测试数据
        test_features = np.random.randn(150, 2048)  # 150个时间步，2048维特征
        print(f"   输入特征形状: {test_features.shape}")
        
        # 池化到100个时间步
        pooled_features = pool_feature_to_100_steps(test_features, num_proposals=100)
        print(f"   池化后特征形状: {pooled_features.shape}")
        
        # 验证形状
        if pooled_features.shape == (100, 2048):
            print("   ✅ 池化函数工作正常")
        else:
            print(f"   ❌ 池化函数输出形状错误，期望(100, 2048)，得到{pooled_features.shape}")
            return False
        
        # 测试边界情况
        print("\n🔧 测试边界情况")
        
        # 单个时间步
        single_step_features = np.random.randn(1, 2048)
        pooled_single = pool_feature_to_100_steps(single_step_features, num_proposals=100)
        print(f"   单时间步输入: {single_step_features.shape} -> {pooled_single.shape}")
        
        if pooled_single.shape == (100, 2048):
            print("   ✅ 单时间步处理正常")
        else:
            print("   ❌ 单时间步处理异常")
            return False
        
        # 少于100个时间步
        short_features = np.random.randn(50, 2048)
        pooled_short = pool_feature_to_100_steps(short_features, num_proposals=100)
        print(f"   短序列输入: {short_features.shape} -> {pooled_short.shape}")
        
        if pooled_short.shape == (100, 2048):
            print("   ✅ 短序列处理正常")
        else:
            print("   ❌ 短序列处理异常")
            return False
        
        print("\n✅ 特征池化函数测试通过")
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_feature_extraction_pipeline():
    """测试特征提取流水线"""
    print("\n🔧 测试特征提取流水线")
    
    # 检查必要的文件是否存在
    config_path = project_root / "data_process" / "preprocessing" / "feature_extraction" / "slowonly_r50_feature_extraction_config.py"
    checkpoint_path = project_root / "work_dirs" / "checkpoints" / "slowonly_r50_kinetics400.pth"
    mmaction2_script = project_root / "mmaction2" / "tools" / "misc" / "clip_feature_extraction.py"
    
    print(f"   配置文件: {config_path.exists()} - {config_path}")
    print(f"   预训练模型: {checkpoint_path.exists()} - {checkpoint_path}")
    print(f"   MMAction2脚本: {mmaction2_script.exists()} - {mmaction2_script}")
    
    if not config_path.exists():
        print("   ⚠️  配置文件不存在，无法测试完整流水线")
        return False
    
    if not checkpoint_path.exists():
        print("   ⚠️  预训练模型不存在，无法测试完整流水线")
        print("   请运行以下命令下载模型:")
        print("   cd data_process/preprocessing/feature_extraction")
        print("   python extract_slowonly_features.py")
        return False
    
    if not mmaction2_script.exists():
        print("   ⚠️  MMAction2特征提取脚本不存在")
        return False
    
    print("   ✅ 所有必要文件都存在，可以进行完整的特征提取")
    return True

def check_inference_script_changes():
    """检查推理脚本的修改"""
    print("\n🔍 检查推理脚本修改")
    
    inference_script = project_root / "data_process" / "inference" / "scripts" / "bmn_inference.py"
    
    if not inference_script.exists():
        print("   ❌ 推理脚本不存在")
        return False
    
    # 读取脚本内容
    with open(inference_script, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键修改
    checks = [
        ("使用MMAction2官方工具", "MMAction2官方特征提取工具" in content),
        ("移除随机特征生成", "np.random.randn(2048)" not in content),
        ("添加池化函数", "pool_feature_to_100_steps" in content),
        ("使用subprocess调用", "subprocess.run(cmd" in content),
        ("处理pkl特征文件", "pickle.load" in content),
        ("与数据预处理一致的参数", "--clip-interval" in content and "--frame-interval" in content),
    ]
    
    all_passed = True
    for check_name, check_result in checks:
        if check_result:
            print(f"   ✅ {check_name}")
        else:
            print(f"   ❌ {check_name}")
            all_passed = False
    
    return all_passed

def main():
    """主函数"""
    print("🎯 BMN推理脚本修复验证")
    print("=" * 60)
    
    # 测试1: 特征提取方法
    test1_passed = test_feature_extraction_method()
    
    # 测试2: 特征提取流水线
    test2_passed = test_feature_extraction_pipeline()
    
    # 测试3: 检查脚本修改
    test3_passed = check_inference_script_changes()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print(f"   特征提取方法测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"   特征提取流水线检查: {'✅ 通过' if test2_passed else '❌ 失败'}")
    print(f"   脚本修改检查: {'✅ 通过' if test3_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed and test3_passed:
        print("\n🎉 所有测试通过！推理脚本已成功修复")
        print("\n📝 修复总结:")
        print("   1. 移除了随机特征生成（第287行）")
        print("   2. 使用MMAction2官方特征提取工具")
        print("   3. 与数据预处理阶段使用相同的参数")
        print("   4. 添加了正确的特征池化函数")
        print("   5. 处理pkl特征文件并转换为CSV格式")
        
        print("\n🚀 下一步:")
        print("   1. 确保预训练模型已下载")
        print("   2. 运行推理脚本测试实际视频")
        print("   3. 验证推理结果的准确性")
    else:
        print("\n⚠️  部分测试未通过，请检查相关问题")

if __name__ == '__main__':
    main()
