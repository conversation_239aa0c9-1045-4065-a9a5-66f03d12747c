#!/usr/bin/env python3
"""
TAD数据集重构脚本
将预分割的动作片段重新组织为完整视频的时间动作定位数据集
"""

import os
import json
import re
import glob
from pathlib import Path
from collections import defaultdict
import cv2
from datetime import datetime, timedelta

class TADDatasetReconstructor:
    def __init__(self, data_root="/home/<USER>/johnny_ws/mmaction2_ws/data/MultiClassTAD",
                 raw_videos_root="/home/<USER>/johnny_ws/mmaction2_ws/data/raw_videos"):
        self.data_root = Path(data_root)
        self.raw_videos_root = Path(raw_videos_root)
        
        # 标签映射
        self.label_mapping = {
            'segmented_videos_ok': 'OK_Action',
            'segmented_videos_nok_appearance': 'NOK_Appearance', 
            'segmented_videos_nok_electric': 'NOK_Electric'
        }
        
        # 视频信息缓存
        self.video_info_cache = {}
        
    def parse_filename(self, filename):
        """
        解析预分割片段文件名
        格式: [时间戳]_decrypted_roi-[起始时间]-[结束时间]-seg[编号].mp4
        或: [时间戳]_decrypted-[起始时间]-[结束时间]-seg[编号]_roi.mp4
        """
        # 移除文件扩展名
        name = Path(filename).stem
        
        # 匹配模式1: timestamp_decrypted_roi-start-end-segN
        pattern1 = r'(\d{8}T\d{6}Z_\d{8}T\d{6}Z)_decrypted_roi-(\d{2}\.\d{2}\.\d{2}\.\d{3})-(\d{2}\.\d{2}\.\d{2}\.\d{3})-seg(\d+)'
        
        # 匹配模式2: timestamp_decrypted-start-end-segN_roi  
        pattern2 = r'(\d{8}T\d{6}Z_\d{8}T\d{6}Z)_decrypted-(\d{2}\.\d{2}\.\d{2}\.\d{3})-(\d{2}\.\d{2}\.\d{2}\.\d{3})-seg(\d+)_roi'
        
        # 匹配模式3: timestamp_decrypted_roi-start-end-segN (无扩展名)
        pattern3 = r'(\d{8}T\d{6}Z_\d{8}T\d{6}Z)_decrypted_roi-(\d{2}\.\d{2}\.\d{2}\.\d{3})-(\d{2}\.\d{2}\.\d{2}\.\d{3})-seg(\d+)'
        
        for pattern in [pattern1, pattern2, pattern3]:
            match = re.match(pattern, name)
            if match:
                timestamp, start_time, end_time, seg_num = match.groups()
                return {
                    'timestamp': timestamp,
                    'start_time': self.time_str_to_seconds(start_time),
                    'end_time': self.time_str_to_seconds(end_time),
                    'segment_number': int(seg_num),
                    'original_name': name
                }
        
        print(f"警告: 无法解析文件名格式: {filename}")
        return None
    
    def time_str_to_seconds(self, time_str):
        """将时间字符串转换为秒数 (格式: HH.MM.SS.mmm)"""
        try:
            parts = time_str.split('.')
            hours = int(parts[0])
            minutes = int(parts[1]) 
            seconds = int(parts[2])
            milliseconds = int(parts[3])
            
            total_seconds = hours * 3600 + minutes * 60 + seconds + milliseconds / 1000.0
            return total_seconds
        except Exception as e:
            print(f"时间转换错误: {time_str}, 错误: {e}")
            return 0.0
    
    def get_video_info(self, video_path):
        """获取视频信息（时长、帧数、帧率）"""
        if str(video_path) in self.video_info_cache:
            return self.video_info_cache[str(video_path)]
            
        try:
            cap = cv2.VideoCapture(str(video_path))
            if not cap.isOpened():
                print(f"无法打开视频文件: {video_path}")
                return None
                
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = frame_count / fps if fps > 0 else 0
            
            cap.release()
            
            info = {
                'duration_second': duration,
                'duration_frame': frame_count,
                'fps': fps,
                'rfps': fps
            }
            
            self.video_info_cache[str(video_path)] = info
            return info
            
        except Exception as e:
            print(f"获取视频信息失败: {video_path}, 错误: {e}")
            return None
    
    def find_original_video(self, timestamp):
        """根据时间戳查找原始视频文件"""
        # 可能的视频格式
        extensions = ['*.mp4', '*.avi', '*.mov', '*.mkv']
        
        for ext in extensions:
            pattern = str(self.raw_videos_root / f"*{timestamp}*{ext}")
            matches = glob.glob(pattern)
            if matches:
                return Path(matches[0])
        
        # 如果直接匹配失败，尝试模糊匹配
        for ext in extensions:
            pattern = str(self.raw_videos_root / f"**/*{timestamp.split('_')[0]}*{ext}")
            matches = glob.glob(pattern, recursive=True)
            if matches:
                return Path(matches[0])
                
        print(f"警告: 未找到时间戳 {timestamp} 对应的原始视频")
        return None
    
    def collect_segments_info(self):
        """收集所有预分割片段的信息"""
        segments_by_video = defaultdict(list)
        
        # 遍历所有标签文件夹
        for label_folder in self.label_mapping.keys():
            folder_path = self.data_root / label_folder
            if not folder_path.exists():
                print(f"文件夹不存在: {folder_path}")
                continue
                
            label = self.label_mapping[label_folder]
            
            # 遍历该文件夹下的所有视频文件
            for video_file in folder_path.glob("*.mp4"):
                parsed = self.parse_filename(video_file.name)
                if parsed:
                    parsed['label'] = label
                    parsed['file_path'] = video_file
                    segments_by_video[parsed['timestamp']].append(parsed)
        
        return segments_by_video
    
    def create_reconstructed_annotations(self, segments_by_video, train_ratio=0.8):
        """创建重构后的标注文件"""
        train_data = {}
        val_data = {}
        
        video_timestamps = list(segments_by_video.keys())
        split_idx = int(len(video_timestamps) * train_ratio)
        
        train_timestamps = video_timestamps[:split_idx]
        val_timestamps = video_timestamps[split_idx:]
        
        for timestamp, segments in segments_by_video.items():
            # 查找原始视频
            original_video = self.find_original_video(timestamp)
            if not original_video:
                continue
                
            # 获取视频信息
            video_info = self.get_video_info(original_video)
            if not video_info:
                continue
            
            # 按时间排序片段
            segments.sort(key=lambda x: x['start_time'])
            
            # 创建标注
            annotations = []
            for segment in segments:
                annotations.append({
                    'segment': [segment['start_time'], segment['end_time']],
                    'label': segment['label']
                })
            
            # 构建完整的视频标注
            video_annotation = {
                'duration_second': video_info['duration_second'],
                'duration_frame': video_info['duration_frame'],
                'annotations': annotations,
                'feature_frame': min(int(video_info['duration_second'] * 10), 3000),  # 10fps特征提取，最大3000帧
                'fps': video_info['fps'],
                'rfps': video_info['rfps'],
                'original_video_path': str(original_video),
                'segments_count': len(segments)
            }
            
            # 分配到训练集或验证集
            if timestamp in train_timestamps:
                train_data[timestamp] = video_annotation
            else:
                val_data[timestamp] = video_annotation
        
        return train_data, val_data
    
    def save_annotations(self, train_data, val_data):
        """保存重构后的标注文件"""
        train_file = self.data_root / "multiclass_tad_train_reconstructed.json"
        val_file = self.data_root / "multiclass_tad_val_reconstructed.json"
        
        with open(train_file, 'w', encoding='utf-8') as f:
            json.dump(train_data, f, indent=2, ensure_ascii=False)
            
        with open(val_file, 'w', encoding='utf-8') as f:
            json.dump(val_data, f, indent=2, ensure_ascii=False)
            
        print(f"训练集标注已保存: {train_file} ({len(train_data)} 个视频)")
        print(f"验证集标注已保存: {val_file} ({len(val_data)} 个视频)")
        
        return train_file, val_file
    
    def generate_statistics(self, train_data, val_data):
        """生成数据集统计信息"""
        def analyze_dataset(data, name):
            total_videos = len(data)
            total_segments = sum(len(v['annotations']) for v in data.values())
            
            label_counts = defaultdict(int)
            duration_stats = []
            
            for video_info in data.values():
                duration_stats.append(video_info['duration_second'])
                for ann in video_info['annotations']:
                    label_counts[ann['label']] += 1
            
            avg_duration = sum(duration_stats) / len(duration_stats) if duration_stats else 0
            
            print(f"\n{name}数据集统计:")
            print(f"  视频数量: {total_videos}")
            print(f"  动作片段总数: {total_segments}")
            print(f"  平均视频时长: {avg_duration:.2f}秒")
            print(f"  标签分布:")
            for label, count in label_counts.items():
                print(f"    {label}: {count}")
        
        analyze_dataset(train_data, "训练集")
        analyze_dataset(val_data, "验证集")
    
    def run(self):
        """执行完整的重构流程"""
        print("开始TAD数据集重构...")
        
        # 1. 收集片段信息
        print("1. 收集预分割片段信息...")
        segments_by_video = self.collect_segments_info()
        print(f"   发现 {len(segments_by_video)} 个原始视频的片段")
        
        # 2. 创建重构标注
        print("2. 创建重构后的标注...")
        train_data, val_data = self.create_reconstructed_annotations(segments_by_video)
        
        # 3. 保存标注文件
        print("3. 保存标注文件...")
        train_file, val_file = self.save_annotations(train_data, val_data)
        
        # 4. 生成统计信息
        print("4. 生成统计信息...")
        self.generate_statistics(train_data, val_data)
        
        print("\n数据集重构完成!")
        return train_file, val_file

if __name__ == "__main__":
    reconstructor = TADDatasetReconstructor()
    reconstructor.run()
