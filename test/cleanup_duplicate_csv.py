#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理重复的CSV文件
删除不必要的 _corrected.csv 文件，保留原始的 .csv 文件
"""

import os
import glob
from pathlib import Path

def cleanup_duplicate_csv_files(directory):
    """清理重复的CSV文件"""
    print(f"🧹 清理目录中的重复CSV文件: {directory}")
    print("=" * 60)
    
    if not os.path.exists(directory):
        print(f"❌ 目录不存在: {directory}")
        return
    
    # 查找所有 _corrected.csv 文件
    corrected_pattern = os.path.join(directory, "*_corrected.csv")
    corrected_files = glob.glob(corrected_pattern)
    
    if not corrected_files:
        print("✅ 没有找到 _corrected.csv 文件，无需清理")
        return
    
    print(f"📋 找到 {len(corrected_files)} 个 _corrected.csv 文件:")
    
    deleted_count = 0
    kept_count = 0
    
    for corrected_file in corrected_files:
        # 获取对应的原始文件路径
        original_file = corrected_file.replace('_corrected.csv', '.csv')
        
        print(f"\n🔍 检查文件对: ")
        print(f"   原始文件: {os.path.basename(original_file)}")
        print(f"   修正文件: {os.path.basename(corrected_file)}")
        
        # 检查原始文件是否存在
        if os.path.exists(original_file):
            # 比较两个文件是否相同
            try:
                import numpy as np
                import pandas as pd
                
                # 读取两个文件
                df_original = pd.read_csv(original_file)
                df_corrected = pd.read_csv(corrected_file)
                
                # 检查形状是否相同
                if df_original.shape == df_corrected.shape:
                    # 检查数据是否相同
                    are_equal = np.allclose(df_original.values, df_corrected.values, rtol=1e-10)
                    
                    if are_equal:
                        # 数据相同，删除 _corrected.csv 文件
                        os.remove(corrected_file)
                        print(f"   ✅ 删除重复文件: {os.path.basename(corrected_file)}")
                        deleted_count += 1
                    else:
                        print(f"   ⚠️  文件内容不同，保留两个文件")
                        kept_count += 1
                else:
                    print(f"   ⚠️  文件形状不同，保留两个文件")
                    print(f"      原始: {df_original.shape}, 修正: {df_corrected.shape}")
                    kept_count += 1
                    
            except Exception as e:
                print(f"   ❌ 比较文件时出错: {e}")
                kept_count += 1
        else:
            print(f"   ⚠️  原始文件不存在，保留修正文件")
            kept_count += 1
    
    print(f"\n" + "=" * 60)
    print(f"📊 清理结果:")
    print(f"   删除的重复文件: {deleted_count}")
    print(f"   保留的文件: {kept_count}")
    print(f"   总处理文件: {len(corrected_files)}")
    
    if deleted_count > 0:
        print(f"\n🎉 清理完成！删除了 {deleted_count} 个重复的 _corrected.csv 文件")
    else:
        print(f"\n✅ 没有删除任何文件")

def main():
    """主函数"""
    print("🎯 CSV文件重复清理工具")
    print("=" * 60)
    
    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    
    # 清理推理结果目录
    inference_features_dir = project_root / "data_process" / "inference" / "scripts" / "inference_results" / "features"
    
    if inference_features_dir.exists():
        cleanup_duplicate_csv_files(str(inference_features_dir))
    else:
        print(f"❌ 推理特征目录不存在: {inference_features_dir}")
    
    # 也可以清理其他可能的目录
    other_dirs = [
        project_root / "test" / "feature_test",
        project_root / "test" / "inference_test" / "features",
        project_root / "test" / "numpy_test",
    ]
    
    for dir_path in other_dirs:
        if dir_path.exists():
            print(f"\n" + "=" * 60)
            cleanup_duplicate_csv_files(str(dir_path))

if __name__ == '__main__':
    main()
