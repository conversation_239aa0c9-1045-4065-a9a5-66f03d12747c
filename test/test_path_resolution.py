#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试路径解析修复
验证所有路径都能正确解析为绝对路径
"""

import os
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_path_resolution():
    """测试路径解析"""
    print("🧪 测试路径解析修复")
    print("=" * 60)
    
    # 模拟推理脚本中的路径解析逻辑
    script_dir = project_root / "data_process" / "inference" / "scripts"
    script_file = script_dir / "bmn_inference.py"
    
    print(f"📁 项目根目录: {project_root}")
    print(f"📁 脚本目录: {script_dir}")
    print(f"📄 脚本文件: {script_file}")
    
    # 测试路径解析逻辑
    print(f"\n🔧 测试路径解析逻辑")
    
    # 模拟脚本中的路径计算
    script_dir_abs = os.path.dirname(os.path.abspath(str(script_file)))
    project_root_abs = os.path.abspath(os.path.join(script_dir_abs, "../../.."))
    
    print(f"   脚本目录绝对路径: {script_dir_abs}")
    print(f"   项目根目录绝对路径: {project_root_abs}")
    
    # 测试关键文件路径
    key_files = {
        "配置文件": os.path.join(project_root_abs, "data_process/preprocessing/feature_extraction/slowonly_r50_feature_extraction_config.py"),
        "预训练模型": os.path.join(project_root_abs, "work_dirs/checkpoints/slowonly_r50_kinetics400.pth"),
        "MMAction2脚本": os.path.join(project_root_abs, "mmaction2/tools/misc/clip_feature_extraction.py"),
        "MMAction2根目录": os.path.join(project_root_abs, "mmaction2"),
    }
    
    print(f"\n📋 检查关键文件路径:")
    all_paths_valid = True
    
    for name, path in key_files.items():
        exists = os.path.exists(path)
        status = "✅" if exists else "❌"
        print(f"   {status} {name}: {path}")
        if not exists and name != "预训练模型":  # 预训练模型可能还没下载
            all_paths_valid = False
    
    # 测试视频路径处理
    print(f"\n🎬 测试视频路径处理:")
    
    test_video_path = "./inference_results/temp_segments/test_video.mp4"
    video_abs_path = os.path.abspath(test_video_path)
    video_dir = os.path.dirname(video_abs_path)
    video_name = os.path.basename(video_abs_path)
    
    print(f"   原始路径: {test_video_path}")
    print(f"   绝对路径: {video_abs_path}")
    print(f"   视频目录: {video_dir}")
    print(f"   视频文件名: {video_name}")
    
    # 测试临时文件路径
    print(f"\n📁 测试临时文件路径:")
    
    output_dir = "./inference_results/features"
    output_dir_abs = os.path.abspath(output_dir)
    temp_video_list = os.path.abspath(os.path.join(output_dir, 'temp_video_list.txt'))
    temp_feature_dir = os.path.abspath(os.path.join(output_dir, 'temp_features'))
    
    print(f"   输出目录: {output_dir_abs}")
    print(f"   临时视频列表: {temp_video_list}")
    print(f"   临时特征目录: {temp_feature_dir}")
    
    # 测试命令构建
    print(f"\n⚙️  测试命令构建:")
    
    mmaction2_root = os.path.abspath(os.path.join(project_root_abs, "mmaction2"))
    extraction_script = os.path.abspath(os.path.join(mmaction2_root, "tools/misc/clip_feature_extraction.py"))
    config_path = key_files["配置文件"]
    checkpoint_path = key_files["预训练模型"]
    
    cmd = [
        'python', extraction_script,
        config_path,
        checkpoint_path,
        temp_feature_dir,
        '--video-list', temp_video_list,
        '--video-root', video_dir,
        '--long-video-mode',
        '--clip-interval', '32',
        '--frame-interval', '2',
        '--spatial-type', 'avg',
        '--temporal-type', 'avg'
    ]
    
    print(f"   工作目录: {mmaction2_root}")
    print(f"   命令: {' '.join(cmd)}")
    
    # 验证命令中的所有路径
    print(f"\n✅ 验证命令中的路径:")
    
    path_checks = [
        ("Python脚本", extraction_script),
        ("配置文件", config_path),
        ("临时特征目录父目录", os.path.dirname(temp_feature_dir)),
        ("临时视频列表父目录", os.path.dirname(temp_video_list)),
        ("视频根目录父目录", os.path.dirname(video_dir)),
        ("工作目录", mmaction2_root),
    ]
    
    cmd_paths_valid = True
    for name, path in path_checks:
        # 对于不存在的目录，检查其父目录
        check_path = path
        if not os.path.exists(path):
            check_path = os.path.dirname(path)
        
        exists = os.path.exists(check_path)
        status = "✅" if exists else "❌"
        print(f"   {status} {name}: {check_path}")
        
        if not exists:
            cmd_paths_valid = False
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"📊 路径解析测试结果:")
    print(f"   关键文件路径: {'✅ 有效' if all_paths_valid else '❌ 部分无效'}")
    print(f"   命令路径: {'✅ 有效' if cmd_paths_valid else '❌ 部分无效'}")
    
    if all_paths_valid and cmd_paths_valid:
        print(f"\n🎉 路径解析修复成功！")
        print(f"   所有路径都已转换为绝对路径")
        print(f"   应该能解决 'No such file or directory' 错误")
    else:
        print(f"\n⚠️  部分路径仍有问题，请检查:")
        if not all_paths_valid:
            print(f"   - 某些关键文件不存在")
        if not cmd_paths_valid:
            print(f"   - 某些命令路径无效")
    
    return all_paths_valid and cmd_paths_valid

def main():
    """主函数"""
    print("🎯 BMN推理脚本路径解析测试")
    print("=" * 60)
    
    success = test_path_resolution()
    
    if success:
        print("\n✅ 路径解析测试通过")
    else:
        print("\n❌ 路径解析测试失败")

if __name__ == '__main__':
    main()
