#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的postprocess_features.py功能
验证扁平化存储和CSV文件命名规范
"""

import os
import sys
import tempfile
import shutil
import pickle
import numpy as np
from pathlib import Path

# 添加项目路径
sys.path.append('data_process/preprocessing/feature_extraction')
from postprocess_features import process_single_video_feature

def create_test_data():
    """创建测试数据"""
    # 创建临时目录结构
    temp_dir = tempfile.mkdtemp()
    
    # 创建输入目录结构（模拟feature_slowonly_raw）
    input_dir = Path(temp_dir) / "feature_slowonly_raw"
    subdir1 = input_dir / "subdir1"
    subdir2 = input_dir / "subdir2"
    
    subdir1.mkdir(parents=True)
    subdir2.mkdir(parents=True)
    
    # 创建输出目录
    output_dir = Path(temp_dir) / "feature_slowonly"
    
    # 创建测试特征数据
    test_features = np.random.rand(10, 2048)  # 10个时间步，2048维特征
    
    # 创建测试pkl文件
    test_files = [
        subdir1 / "video1.mp4.pkl",
        subdir1 / "video2.pkl", 
        subdir2 / "video3.mp4.pkl",
        subdir2 / "video4.pkl"
    ]
    
    for pkl_file in test_files:
        with open(pkl_file, 'wb') as f:
            pickle.dump(test_features, f)
    
    return temp_dir, input_dir, output_dir, test_files

def test_flat_storage():
    """测试扁平化存储功能"""
    print("🧪 测试扁平化存储功能...")
    
    temp_dir, input_dir, output_dir, test_files = create_test_data()
    
    try:
        # 处理所有测试文件
        for pkl_file in test_files:
            success = process_single_video_feature(str(pkl_file), str(output_dir), str(input_dir))
            if not success:
                print(f"❌ 处理文件失败: {pkl_file}")
                return False
        
        # 检查输出结构
        csv_files = list(output_dir.rglob("*.csv"))
        
        print(f"📊 生成的CSV文件数量: {len(csv_files)}")
        
        # 验证所有文件都在根目录
        root_files = [f for f in csv_files if f.parent == output_dir]
        subdir_files = [f for f in csv_files if f.parent != output_dir]
        
        print(f"   根目录中的文件: {len(root_files)}")
        print(f"   子目录中的文件: {len(subdir_files)}")
        
        if subdir_files:
            print("❌ 发现子目录中的文件，扁平化存储失败:")
            for f in subdir_files:
                print(f"   {f}")
            return False
        
        print("✅ 扁平化存储测试通过")
        return True
        
    finally:
        # 清理临时目录
        shutil.rmtree(temp_dir)

def test_csv_naming():
    """测试CSV文件命名规范"""
    print("\n🧪 测试CSV文件命名规范...")
    
    temp_dir, input_dir, output_dir, test_files = create_test_data()
    
    try:
        # 处理所有测试文件
        for pkl_file in test_files:
            success = process_single_video_feature(str(pkl_file), str(output_dir), str(input_dir))
            if not success:
                print(f"❌ 处理文件失败: {pkl_file}")
                return False
        
        # 检查文件命名
        csv_files = list(output_dir.glob("*.csv"))
        expected_names = ["video1.csv", "video2.csv", "video3.csv", "video4.csv"]
        actual_names = [f.name for f in csv_files]
        
        print(f"📊 期望的文件名: {expected_names}")
        print(f"📊 实际的文件名: {actual_names}")
        
        # 检查是否有.mp4.csv格式的文件
        mp4_csv_files = [name for name in actual_names if '.mp4.csv' in name]
        if mp4_csv_files:
            print(f"❌ 发现包含.mp4.csv的文件: {mp4_csv_files}")
            return False
        
        # 检查所有期望的文件是否存在
        missing_files = set(expected_names) - set(actual_names)
        if missing_files:
            print(f"❌ 缺少期望的文件: {missing_files}")
            return False
        
        print("✅ CSV文件命名规范测试通过")
        return True
        
    finally:
        # 清理临时目录
        shutil.rmtree(temp_dir)

def test_csv_content():
    """测试CSV文件内容格式"""
    print("\n🧪 测试CSV文件内容格式...")
    
    temp_dir, input_dir, output_dir, test_files = create_test_data()
    
    try:
        # 处理一个测试文件
        pkl_file = test_files[0]
        success = process_single_video_feature(str(pkl_file), str(output_dir), str(input_dir))
        
        if not success:
            print(f"❌ 处理文件失败: {pkl_file}")
            return False
        
        # 检查生成的CSV文件
        csv_files = list(output_dir.glob("*.csv"))
        if not csv_files:
            print("❌ 没有生成CSV文件")
            return False
        
        csv_file = csv_files[0]
        
        # 读取CSV内容
        with open(csv_file, 'r') as f:
            lines = f.readlines()
        
        print(f"📊 CSV文件行数: {len(lines)}")
        
        # 检查header
        header = lines[0].strip()
        expected_header_parts = header.split(',')
        
        if len(expected_header_parts) != 100:
            print(f"❌ Header列数错误: {len(expected_header_parts)}, 期望100")
            return False
        
        # 检查数据行数（应该是2048行特征）
        data_lines = len(lines) - 1  # 减去header行
        if data_lines != 2048:
            print(f"❌ 数据行数错误: {data_lines}, 期望2048")
            return False
        
        print("✅ CSV文件内容格式测试通过")
        return True
        
    finally:
        # 清理临时目录
        shutil.rmtree(temp_dir)

def main():
    """运行所有测试"""
    print("=" * 60)
    print("🧪 测试修改后的postprocess_features.py功能")
    print("=" * 60)
    
    tests = [
        test_flat_storage,
        test_csv_naming,
        test_csv_content
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ 测试失败: {test_func.__name__}")
        except Exception as e:
            print(f"❌ 测试异常: {test_func.__name__} - {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！修改功能正常")
        return True
    else:
        print("❌ 部分测试失败，请检查修改")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
