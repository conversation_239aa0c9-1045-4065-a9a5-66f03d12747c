#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成符合ActivityNet格式的标注文件
用于BMN时间动作定位任务
"""

import json
import os
import cv2
from pathlib import Path
import argparse

def get_video_info(video_path):
    """
    获取视频的基本信息
    
    Args:
        video_path (str): 视频文件路径
    
    Returns:
        dict: 包含duration_second, duration_frame, fps等信息
    """
    try:
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print(f"无法打开视频: {video_path}")
            return None
        
        # 获取视频信息
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration_second = frame_count / fps if fps > 0 else 0
        
        cap.release()
        
        return {
            'duration_second': duration_second,
            'duration_frame': frame_count,
            'fps': fps,
            'rfps': fps  # real fps，这里假设与fps相同
        }
        
    except Exception as e:
        print(f"获取视频信息时出错 {video_path}: {e}")
        return None

def generate_annotations():
    """生成ActivityNet格式的标注文件"""
    
    # 数据根目录
    data_root = '../../../data/MultiClassTAD'
    
    # 类别映射
    category_mapping = {
        'segmented_videos_ok': 'OK_Action',
        'segmented_videos_nok_electric': 'NOK_Electric_Action', 
        'segmented_videos_nok_appearance': 'NOK_Appearance_Action'
    }
    
    # 三个类别目录
    categories = [
        'segmented_videos_ok',
        'segmented_videos_nok_electric',
        'segmented_videos_nok_appearance'
    ]
    
    annotations = {}
    
    for category in categories:
        category_path = Path(data_root) / category
        if not category_path.exists():
            print(f"警告: 目录不存在 {category_path}")
            continue
        
        # 获取该类别下的所有mp4文件
        video_files = list(category_path.glob('*.mp4'))
        
        for video_file in video_files:
            # 获取视频信息
            video_info = get_video_info(str(video_file))
            if video_info is None:
                continue
            
            # 生成视频ID（去掉扩展名）
            video_id = video_file.stem
            
            # 对于时间动作定位，我们假设整个视频片段就是一个动作
            # 因为这些是已经分割好的动作片段
            annotations[video_id] = {
                'duration_second': video_info['duration_second'],
                'duration_frame': video_info['duration_frame'],
                'annotations': [
                    {
                        'segment': [0.0, video_info['duration_second']],  # 整个视频都是动作
                        'label': category_mapping[category]
                    }
                ],
                'feature_frame': 100,  # BMN使用100个时间步的特征
                'fps': video_info['fps'],
                'rfps': video_info['rfps']
            }
    
    # 分割训练集和验证集 (80%训练，20%验证)
    video_ids = list(annotations.keys())
    split_point = int(len(video_ids) * 0.8)
    
    train_ids = video_ids[:split_point]
    val_ids = video_ids[split_point:]
    
    # 生成训练集标注
    train_annotations = {vid: annotations[vid] for vid in train_ids}
    train_file = Path(data_root) / 'multiclass_tad_train.json'
    with open(train_file, 'w', encoding='utf-8') as f:
        json.dump(train_annotations, f, indent=2, ensure_ascii=False)
    
    # 生成验证集标注
    val_annotations = {vid: annotations[vid] for vid in val_ids}
    val_file = Path(data_root) / 'multiclass_tad_val.json'
    with open(val_file, 'w', encoding='utf-8') as f:
        json.dump(val_annotations, f, indent=2, ensure_ascii=False)
    
    print(f"生成标注文件:")
    print(f"  训练集: {train_file} ({len(train_annotations)} 个视频)")
    print(f"  验证集: {val_file} ({len(val_annotations)} 个视频)")
    
    # 统计各类别数量
    def count_categories(annotations_dict):
        counts = {'OK_Action': 0, 'NOK_Electric_Action': 0, 'NOK_Appearance_Action': 0}
        for video_data in annotations_dict.values():
            for ann in video_data['annotations']:
                label = ann['label']
                if label in counts:
                    counts[label] += 1
        return counts
    
    train_counts = count_categories(train_annotations)
    val_counts = count_categories(val_annotations)
    
    print(f"\n训练集类别分布:")
    for label, count in train_counts.items():
        print(f"  {label}: {count}")
    
    print(f"\n验证集类别分布:")
    for label, count in val_counts.items():
        print(f"  {label}: {count}")
    
    return train_file, val_file

def main():
    parser = argparse.ArgumentParser(description='生成ActivityNet格式标注文件')
    
    args = parser.parse_args()
    
    print("开始生成ActivityNet格式标注文件...")
    
    try:
        train_file, val_file = generate_annotations()
        print(f"\n标注文件生成完成!")
        print(f"训练集标注: {train_file}")
        print(f"验证集标注: {val_file}")
        
    except Exception as e:
        print(f"生成标注文件时出错: {e}")

if __name__ == '__main__':
    main()
