#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为测试视频创建模拟特征文件
用于验证BMN推理流程
"""

import os
import numpy as np
from pathlib import Path

def create_test_features(video_path: str, output_dir: str, duration_seconds: float = 60.0) -> str:
    """
    为测试视频创建模拟的SlowOnly特征文件
    
    Args:
        video_path: 视频文件路径
        output_dir: 输出目录
        duration_seconds: 视频时长（秒）
    
    Returns:
        特征文件路径
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成特征文件路径
    video_name = Path(video_path).stem
    feature_file = os.path.join(output_dir, f"{video_name}.csv")
    
    print(f"🎭 创建测试特征文件...")
    print(f"   视频: {video_path}")
    print(f"   时长: {duration_seconds}秒")
    print(f"   输出: {feature_file}")
    
    # 计算特征维度
    # BMN通常使用100个时间步
    temporal_dim = 100
    feature_dim = 2048  # SlowOnly R50特征维度
    
    # 生成随机特征（模拟真实特征的分布）
    # 使用正态分布，均值0，标准差0.1
    features = np.random.normal(0, 0.1, (feature_dim, temporal_dim)).astype(np.float32)
    
    # 添加一些结构化的模式，模拟真实的动作特征
    # 在某些时间段添加更强的信号
    action_segments = [
        (10, 25),   # 第一个动作段
        (35, 50),   # 第二个动作段
        (70, 85),   # 第三个动作段
    ]
    
    for start, end in action_segments:
        start_idx = int(start * temporal_dim / 100)
        end_idx = int(end * temporal_dim / 100)
        
        # 在动作段增强特征信号
        features[:, start_idx:end_idx] += np.random.normal(0, 0.3, 
                                                          (feature_dim, end_idx - start_idx))
    
    # 保存为CSV格式（不包含header，跳过第一行）
    # 第一行是占位符header
    header = ','.join([f'feature_{i}' for i in range(temporal_dim)])
    
    # 保存特征
    with open(feature_file, 'w') as f:
        f.write(header + '\n')
        for i in range(feature_dim):
            row = ','.join([f'{features[i, j]:.6f}' for j in range(temporal_dim)])
            f.write(row + '\n')
    
    print(f"✅ 特征文件创建完成")
    print(f"   特征形状: ({feature_dim}, {temporal_dim})")
    print(f"   文件大小: {os.path.getsize(feature_file) / 1024:.1f} KB")
    
    return feature_file

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='创建测试特征文件')
    parser.add_argument('--video', required=True, help='视频文件路径')
    parser.add_argument('--output-dir', default='./inference_results/features', help='输出目录')
    parser.add_argument('--duration', type=float, default=60.0, help='视频时长（秒）')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.video):
        print(f"❌ 视频文件不存在: {args.video}")
        return
    
    feature_file = create_test_features(args.video, args.output_dir, args.duration)
    print(f"\n🎉 测试特征文件已创建: {feature_file}")
    print("现在可以使用此特征文件进行BMN推理测试")

if __name__ == '__main__':
    main()
