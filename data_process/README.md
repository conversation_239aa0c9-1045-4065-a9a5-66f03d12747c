# BMN时间动作定位 - 数据处理工具集

本项目基于MMAction2官方文档，使用SlowOnly预训练模型为BMN时间动作定位任务提供完整的数据处理解决方案。

## 项目概述

- **数据**: 三类工人动作视频（OK、NOK电气、NOK外观）
- **模型**: SlowOnly R50 (Kinetics-400预训练) + BMN时间动作定位
- **特征**: 2048维，100时间步
- **格式**: CSV文件，符合BMN LoadLocalizationFeature要求
- **目标**: 完整的时间动作定位训练和推理流程

## 重新组织后的目录结构

```
data_process/
├── README.md                           # 本文档
├── preprocessing/                      # 数据预处理
│   ├── data_preparation/              # 数据准备
│   │   ├── generate_video_list.py            # 生成视频列表
│   │   └── generate_activitynet_annotations.py  # 生成标注文件
│   ├── feature_extraction/            # 特征提取
│   │   ├── extract_slowonly_features.py     # SlowOnly特征提取
│   │   ├── slowonly_r50_feature_extraction_config.py  # 特征提取配置
│   │   └── postprocess_features.py          # 特征后处理
│   └── data_organization/              # 数据整理
│       ├── move_csv_to_root.py              # 移动CSV文件
│       └── rename_csv_files.py              # 重命名CSV文件
├── training/                           # 模型训练
│   ├── configs/                       # 训练配置
│   │   ├── bmn_multiclass_tad_config.py     # BMN训练配置
│   │   └── fix_transforms.py                # 自定义变换
│   └── scripts/                       # 训练脚本
│       ├── train_bmn.py                     # BMN训练脚本
│       └── start_bmn_training.sh            # 训练启动脚本
├── inference/                          # 模型推理
│   ├── scripts/                       # 推理脚本
│   │   ├── bmn_inference.py                 # BMN推理脚本
│   │   └── create_test_features.py          # 创建测试特征
│   └── configs/                       # 推理配置（预留）
└── utilities/                          # 工具集
    ├── validation/                    # 验证工具
    │   └── validate_features.py             # 特征格式验证
    ├── testing/                       # 测试工具
    │   ├── test_config.py                   # 配置文件测试
    │   └── test_feature_shape.py            # 特征形状测试
    └── pipeline/                      # 流水线工具
        └── run_feature_extraction_pipeline.py  # 完整流水线

data/MultiClassTAD/
├── segmented_videos_ok/                # OK类别视频
├── segmented_videos_nok_electric/      # NOK电气类别视频
├── segmented_videos_nok_appearance/    # NOK外观类别视频
├── features_slowonly/                  # 提取的CSV特征文件
├── video_list.txt                      # 视频列表文件
├── multiclass_tad_train.json          # 训练集标注
└── multiclass_tad_val.json            # 验证集标注
```

## 快速开始

### 1. 运行完整流水线

```bash
cd utilities/pipeline
python run_feature_extraction_pipeline.py
```

### 2. 分步执行

如果需要分步执行或调试：

```bash
# 步骤1: 生成视频列表
cd preprocessing/data_preparation
python generate_video_list.py

# 步骤2: 生成标注文件
python generate_activitynet_annotations.py

# 步骤3: 提取特征（需要GPU）
cd ../feature_extraction
python extract_slowonly_features.py

# 步骤4: 后处理特征
python postprocess_features.py

# 步骤5: 验证特征格式
cd ../../utilities/validation
python validate_features.py
```

### 3. 只验证现有特征

```bash
cd utilities/pipeline
python run_feature_extraction_pipeline.py --only-validate
```

### 4. 训练BMN模型

```bash
cd training/scripts
python train_bmn.py
# 或使用Shell脚本
bash start_bmn_training.sh
```

### 5. 运行推理

```bash
cd inference/scripts
python bmn_inference.py --video /path/to/video.mp4
```

## 技术规范

### SlowOnly模型配置
- **模型**: ResNet3dSlowOnly (depth=50)
- **预训练**: Kinetics-400数据集
- **输入**: 8帧，224x224分辨率
- **输出**: 2048维特征向量

### 特征提取参数
- **长视频模式**: 启用，支持任意长度视频
- **时间采样**: clip_interval=16, frame_interval=1
- **空间池化**: 平均池化
- **时间池化**: 保持原始时间维度

### BMN兼容格式
- **特征维度**: 2048 (行) × 100 (列)
- **文件格式**: CSV，包含header行
- **时间步数**: 100个固定时间步
- **数值精度**: 6位小数

## 数据统计

根据当前数据集：
- **总视频数**: 132个
- **OK类别**: 124个视频
- **NOK电气**: 1个视频  
- **NOK外观**: 7个视频
- **训练/验证分割**: 80%/20%

## 各模块详细说明

### 数据预处理模块 (preprocessing/)

#### 数据准备 (data_preparation/)
- **generate_video_list.py**: 扫描视频目录，生成MMAction2格式的视频列表
- **generate_activitynet_annotations.py**: 生成ActivityNet格式的标注文件

#### 特征提取 (feature_extraction/)
- **extract_slowonly_features.py**: 使用SlowOnly R50提取视频特征
- **slowonly_r50_feature_extraction_config.py**: SlowOnly特征提取配置
- **postprocess_features.py**: 将特征转换为BMN所需的CSV格式

#### 数据整理 (data_organization/)
- **move_csv_to_root.py**: 将CSV文件从子目录移动到根目录
- **rename_csv_files.py**: 统一CSV文件名格式

### 训练模块 (training/)

#### 配置文件 (configs/)
- **bmn_multiclass_tad_config.py**: BMN训练的完整配置
- **fix_transforms.py**: 修复BMN输入维度的自定义变换

#### 训练脚本 (scripts/)
- **train_bmn.py**: Python训练脚本，支持参数配置
- **start_bmn_training.sh**: Shell训练脚本，一键启动

### 推理模块 (inference/)

#### 推理脚本 (scripts/)
- **bmn_inference.py**: 完整的BMN推理流程，包括特征提取和视频分割
- **create_test_features.py**: 为测试视频创建模拟特征

### 工具模块 (utilities/)

#### 验证工具 (validation/)
- **validate_features.py**: 验证特征文件格式和标注文件一致性

#### 测试工具 (testing/)
- **test_config.py**: 测试配置文件是否能正常加载
- **test_feature_shape.py**: 测试特征形状是否符合BMN要求

#### 流水线工具 (pipeline/)
- **run_feature_extraction_pipeline.py**: 完整的特征提取流水线

## 重组后的优势

### 1. 清晰的功能分离
- **预处理**: 专注于数据准备和特征提取
- **训练**: 专注于模型训练相关配置和脚本
- **推理**: 专注于模型推理和结果处理
- **工具**: 提供验证、测试和流水线工具

### 2. 更好的可维护性
- 相关功能集中在同一目录下
- 减少了文件查找的复杂性
- 便于添加新功能和脚本

### 3. 标准化的工作流程
- 每个阶段都有明确的输入输出
- 支持分步执行和完整流水线
- 便于调试和问题定位

## 故障排除

### 1. 导入路径问题
- 确保在正确的目录下运行脚本
- 检查相对路径是否正确
- 验证Python路径设置

### 2. 特征提取失败
- 检查GPU内存是否足够
- 确认MMAction2环境正确安装
- 验证预训练模型下载是否成功

### 3. 特征格式错误
- 运行验证工具检查具体错误：`cd utilities/validation && python validate_features.py`
- 确认CSV文件格式：2048行×100列
- 检查是否存在NaN或无穷大值

### 4. 配置文件加载失败
- 运行配置测试：`cd utilities/testing && python test_config.py`
- 检查fix_transforms.py是否在正确位置
- 验证MMAction2环境是否正确

## 依赖要求

- Python 3.8+
- MMAction2
- PyTorch
- OpenCV
- NumPy
- Pandas
- SciPy

## 参考文档

- [MMAction2官方文档](https://mmaction2.readthedocs.io/en/latest/)
- [BMN论文](https://openaccess.thecvf.com/content_ICCV_2019/html/Lin_BMN_Boundary-Matching_Network_for_Temporal_Action_Proposal_Generation_ICCV_2019_paper.html)
- [SlowOnly论文](https://openaccess.thecvf.com/content_ICCV_2019/html/Feichtenhofer_SlowFast_Networks_for_Video_Recognition_ICCV_2019_paper.html)
