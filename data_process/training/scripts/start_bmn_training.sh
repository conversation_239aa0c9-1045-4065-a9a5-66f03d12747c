#!/bin/bash

# BMN训练启动脚本
# 用于多类别时间动作定位任务

echo "============================================================"
echo "🎯 BMN多类别时间动作定位训练"
echo "============================================================"

# 激活conda环境
echo "🔧 激活conda环境..."
source ~/anaconda3/etc/profile.d/conda.sh
conda activate mmaction2-tad

# 检查当前目录
if [ ! -f "../configs/bmn_multiclass_tad_config.py" ]; then
    echo "❌ 配置文件不存在，请确保在training/scripts目录下运行"
    exit 1
fi

# 创建工作目录
echo "📁 创建工作目录..."
mkdir -p ../../../work_dirs/bmn_multiclass_tad_slowonly

# 检查GPU
echo "🔍 检查GPU状态..."
nvidia-smi

echo ""
echo "🚀 开始BMN训练..."
echo "   配置文件: ../configs/bmn_multiclass_tad_config.py"
echo "   工作目录: ../../../work_dirs/bmn_multiclass_tad_slowonly"
echo "============================================================"

# 启动训练
cd /home/<USER>/johnny_ws/mmaction2_ws/data_process/training/scripts
export CUDA_VISIBLE_DEVICES=0
python ../../../mmaction2/tools/train.py ../configs/bmn_multiclass_tad_config.py

echo ""
echo "🎉 训练完成!"
echo "============================================================"
echo "📋 接下来你可以:"
echo "   1. 查看训练日志: ../../../work_dirs/bmn_multiclass_tad_slowonly/"
echo "   2. 使用最佳模型进行测试"
echo "   3. 分析训练指标和损失曲线"
echo "============================================================"
