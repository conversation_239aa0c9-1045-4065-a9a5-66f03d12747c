# BMN最终修复配置
_base_ = [
    '../../../mmaction2/configs/_base_/models/bmn_400x100.py',
    '../../../mmaction2/configs/_base_/default_runtime.py'
]

# 导入自定义变换
import sys
import os
config_dir = os.path.dirname(os.path.abspath(__file__)) if '__file__' in globals() else os.path.join(os.getcwd(), 'data_process/training/configs')
sys.path.insert(0, config_dir)
import corrected_transforms  # noqa: F401

# 模型配置 - 减少复杂度以适应小数据集
model = dict(
    type='BMN',
    temporal_dim=100,
    boundary_ratio=0.5,
    num_samples=16,  # 减少采样数
    num_samples_per_bin=2,  # 减少bin采样
    feat_dim=2048,
    soft_nms_alpha=0.4,
    soft_nms_low_threshold=0.5,
    soft_nms_high_threshold=0.9,
    post_process_top_k=50,  # 减少后处理数量
    hidden_dim_1d=128,  # 减少隐藏层维度
    hidden_dim_2d=64,
    hidden_dim_3d=256
)

# 数据集配置 - 使用修复后的标注文件
dataset_type = 'ActivityNetDataset'
data_root = 'data/MultiClassTAD/features_slowonly_reconstructed'
ann_file_train = 'data/MultiClassTAD/multiclass_tad_train_reconstructed_fixed.json'
ann_file_val = 'data/MultiClassTAD/multiclass_tad_val_reconstructed_fixed.json'

# 数据管道 - 使用自定义变换解决维度不匹配问题
train_pipeline = [
    dict(type='LoadLocalizationFeatureForBMN'),  # 使用自定义变换
    dict(type='GenerateLocalizationLabels'),
    dict(type='PackLocalizationInputs', keys=('gt_bbox',), meta_keys=('video_name',))
]

val_pipeline = [
    dict(type='LoadLocalizationFeatureForBMN'),  # 使用自定义变换
    dict(type='GenerateLocalizationLabels'),
    dict(type='PackLocalizationInputs',
         keys=('gt_bbox',),
         meta_keys=('video_name', 'duration_second', 'duration_frame', 'annotations', 'feature_frame'))
]

# 数据加载器
train_dataloader = dict(
    batch_size=1,
    num_workers=1,
    persistent_workers=False,
    sampler=dict(type='DefaultSampler', shuffle=True),
    dataset=dict(
        type=dataset_type,
        ann_file=ann_file_train,
        data_prefix=dict(video=data_root),
        pipeline=train_pipeline
    )
)

val_dataloader = dict(
    batch_size=1,
    num_workers=1,
    persistent_workers=False,
    sampler=dict(type='DefaultSampler', shuffle=False),
    dataset=dict(
        type=dataset_type,
        ann_file=ann_file_val,
        data_prefix=dict(video=data_root),
        pipeline=val_pipeline,
        test_mode=True
    )
)

# 训练配置
max_epochs = 3  # 减少训练轮数用于测试
train_cfg = dict(type='EpochBasedTrainLoop', max_epochs=max_epochs, val_begin=1, val_interval=1)
val_cfg = dict(type='ValLoop')

# 优化器配置 - 更保守的设置
optim_wrapper = dict(
    optimizer=dict(type='Adam', lr=0.00001, weight_decay=0.01),  # 更低的学习率
    clip_grad=dict(max_norm=1.0, norm_type=2)  # 更强的梯度裁剪
)

# 学习率调度
param_scheduler = [
    dict(type='StepLR', begin=0, end=max_epochs, by_epoch=True, step_size=2, gamma=0.5)
]

# 评估器
val_evaluator = dict(
    type='ANetMetric',
    metric_type='AR@AN',
    dump_config=dict(out='work_dirs/bmn_final/results.json', output_format='json')
)

# 工作目录
work_dir = 'work_dirs/bmn_final'

# 钩子配置
default_hooks = dict(
    runtime_info=dict(type='RuntimeInfoHook'),
    timer=dict(type='IterTimerHook'),
    logger=dict(type='LoggerHook', interval=1, ignore_last=False),
    param_scheduler=dict(type='ParamSchedulerHook'),
    checkpoint=dict(type='CheckpointHook', interval=1, save_best='auto'),
    sampler_seed=dict(type='DistSamplerSeedHook')
)

# 日志配置
log_processor = dict(type='LogProcessor', window_size=1, by_epoch=True)
log_level = 'INFO'
